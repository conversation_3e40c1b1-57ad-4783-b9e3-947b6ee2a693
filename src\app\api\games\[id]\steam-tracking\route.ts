import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase/server';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const regionCode = searchParams.get('region') || 'br';
    
    // Validate game ID
    if (!params.id) {
      return NextResponse.json(
        { success: false, error: 'Game ID is required' },
        { status: 400 }
      );
    }

    // Validate region code
    const validRegions = [
      'br', 'us', 'eu', 'de', 'gb', 'jp', 'ca', 'au', 'mx', 'ar', 'cl', 'co', 'pe', 'uy',
      'kr', 'cn', 'in', 'ru', 'pl', 'th', 'sg', 'hk', 'tw', 'nz', 'no', 'se', 'dk', 'fi',
      'ch', 'tr', 'ua'
    ];
    
    if (!validRegions.includes(regionCode)) {
      return NextResponse.json(
        { success: false, error: 'Invalid region code' },
        { status: 400 }
      );
    }

    console.log(`🔍 API: Fetching Steam price tracking for game ${params.id} in region ${regionCode}`);

    const supabase = await createServerClient();
    
    // Get steam price tracking data
    const { data: trackingData, error: trackingError } = await supabase
      .rpc('get_steam_price_data_for_chart', {
        p_game_id: params.id,
        p_region_code: regionCode
      });

    if (trackingError) {
      console.error('Error fetching Steam price tracking:', trackingError);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch Steam price tracking data' },
        { status: 500 }
      );
    }

    if (!trackingData || trackingData.length === 0) {
      return NextResponse.json({
        success: true,
        data: {
          gameId: params.id,
          regionCode,
          hasData: false,
          message: 'No Steam price data available for this region'
        }
      });
    }

    const tracking = trackingData[0];

    // Helper function to format price
    const formatPrice = (priceCents: number, currency: string): string => {
      const price = priceCents / 100;
      
      switch (currency) {
        case 'BRL':
          return `R$ ${price.toFixed(2).replace('.', ',')}`;
        case 'USD':
          return `$${price.toFixed(2)}`;
        case 'EUR':
          return `€${price.toFixed(2)}`;
        case 'GBP':
          return `£${price.toFixed(2)}`;
        case 'JPY':
          return `¥${Math.round(price)}`;
        default:
          return `${price.toFixed(2)} ${currency}`;
      }
    };

    // Format response data
    const response = {
      success: true,
      data: {
        gameId: params.id,
        regionCode,
        currency: tracking.currency,
        hasData: true,
        prices: {
          original: tracking.original_price_cents ? {
            cents: tracking.original_price_cents,
            formatted: formatPrice(tracking.original_price_cents, tracking.currency),
            updated: tracking.original_price_updated
          } : null,
          allTimeLow: tracking.all_time_low_cents ? {
            cents: tracking.all_time_low_cents,
            formatted: formatPrice(tracking.all_time_low_cents, tracking.currency),
            updated: tracking.all_time_low_updated
          } : null,
          allTimeHigh: tracking.all_time_high_cents ? {
            cents: tracking.all_time_high_cents,
            formatted: formatPrice(tracking.all_time_high_cents, tracking.currency),
            updated: tracking.all_time_high_updated
          } : null
        },
        // Create chart data points from the 3 values
        chartData: [
          tracking.original_price_cents && {
            type: 'original',
            price_cents: tracking.original_price_cents,
            price_formatted: formatPrice(tracking.original_price_cents, tracking.currency),
            recorded_at: tracking.original_price_updated,
            color: '#8b5cf6'
          },
          tracking.all_time_low_cents && {
            type: 'low',
            price_cents: tracking.all_time_low_cents,
            price_formatted: formatPrice(tracking.all_time_low_cents, tracking.currency),
            recorded_at: tracking.all_time_low_updated,
            color: '#10b981'
          },
          tracking.all_time_high_cents && {
            type: 'high',
            price_cents: tracking.all_time_high_cents,
            price_formatted: formatPrice(tracking.all_time_high_cents, tracking.currency),
            recorded_at: tracking.all_time_high_updated,
            color: '#ef4444'
          }
        ].filter(Boolean) // Remove null entries
      }
    };

    console.log(`✅ API: Successfully fetched Steam price tracking with ${response.data.chartData.length} data points`);

    return NextResponse.json(response, {
      headers: {
        'Cache-Control': 'public, s-maxage=1800, stale-while-revalidate=3600', // Cache for 30 minutes
      },
    });

  } catch (error) {
    console.error('Error in Steam price tracking API:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error while fetching Steam price tracking',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}