-- Fix Steam Price Storage - Add missing columns and update trigger
-- This migration ensures original prices are properly stored

-- 1. Add region_code to game_prices table if missing
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'game_prices' AND column_name = 'region_code') THEN
        ALTER TABLE game_prices ADD COLUMN region_code VARCHAR(5) DEFAULT 'br';
        -- Update unique constraint to include region_code
        ALTER TABLE game_prices DROP CONSTRAINT IF EXISTS game_prices_game_id_store_name_key;
        ALTER TABLE game_prices ADD CONSTRAINT unique_game_store_region UNIQUE(game_id, store_name, region_code);
        -- Add index for region lookups
        CREATE INDEX IF NOT EXISTS idx_game_prices_region_code ON game_prices(region_code);
    END IF;
END $$;

-- 2. Add price_type to steam_price_history table if missing
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'steam_price_history' AND column_name = 'price_type') THEN
        ALTER TABLE steam_price_history ADD COLUMN price_type VARCHAR(20) DEFAULT 'regular';
    END IF;
END $$;

-- 3. Drop existing trigger and function to recreate them
DROP TRIGGER IF EXISTS trigger_archive_steam_price_history ON game_prices;
DROP FUNCTION IF EXISTS archive_steam_price_to_history();

-- 4. Create improved function to archive Steam prices
CREATE OR REPLACE FUNCTION archive_steam_price_to_history()
RETURNS TRIGGER AS $$
DECLARE
    new_price_cents INTEGER;
    current_lowest_price INTEGER;
    current_highest_price INTEGER;
    should_record BOOLEAN := FALSE;
    record_type VARCHAR(20) := 'regular';
BEGIN
    -- Only archive Steam prices to history
    IF NEW.store_name = 'Steam' THEN
        -- Convert new price string to cents - handle multiple currencies
        BEGIN
            new_price_cents := CASE 
                WHEN NEW.currency = 'BRL' THEN 
                    CAST(REPLACE(REPLACE(NEW.price, 'R$ ', ''), ',', '.') AS DECIMAL) * 100
                WHEN NEW.currency = 'USD' THEN 
                    CAST(REPLACE(NEW.price, '$', '') AS DECIMAL) * 100
                WHEN NEW.currency = 'EUR' THEN 
                    CAST(REPLACE(REPLACE(NEW.price, '€', ''), ',', '.') AS DECIMAL) * 100
                WHEN NEW.currency = 'GBP' THEN 
                    CAST(REPLACE(NEW.price, '£', '') AS DECIMAL) * 100
                WHEN NEW.currency = 'JPY' THEN 
                    CAST(REPLACE(NEW.price, '¥', '') AS DECIMAL) * 100
                WHEN NEW.currency = 'CAD' THEN 
                    CAST(REPLACE(NEW.price, 'CDN$ ', '') AS DECIMAL) * 100
                WHEN NEW.currency = 'AUD' THEN 
                    CAST(REPLACE(NEW.price, 'A$ ', '') AS DECIMAL) * 100
                ELSE 
                    CAST(REGEXP_REPLACE(NEW.price, '[^0-9.]', '', 'g') AS DECIMAL) * 100
            END;
        EXCEPTION
            WHEN OTHERS THEN
                RAISE WARNING 'Failed to parse Steam price: % for currency: %', NEW.price, NEW.currency;
                RETURN NEW;
        END;

        -- Get current lowest and highest prices for this game/region
        SELECT 
            MIN(price_cents), 
            MAX(price_cents) 
        INTO current_lowest_price, current_highest_price
        FROM steam_price_history 
        WHERE game_id = NEW.game_id 
            AND region_code = COALESCE(NEW.region_code, 'br');

        -- Determine if we should record this price change
        IF current_lowest_price IS NULL OR current_highest_price IS NULL THEN
            -- First price record for this game/region
            should_record := TRUE;
            record_type := 'initial';
        ELSIF new_price_cents < current_lowest_price THEN
            -- New lowest price
            should_record := TRUE;
            record_type := 'lowest';
        ELSIF new_price_cents > current_highest_price THEN
            -- New highest price
            should_record := TRUE;
            record_type := 'highest';
        ELSE
            -- Check if this is a significant price change (more than 5% from last recorded price)
            DECLARE
                last_recorded_price INTEGER;
            BEGIN
                SELECT price_cents INTO last_recorded_price
                FROM steam_price_history 
                WHERE game_id = NEW.game_id 
                    AND region_code = COALESCE(NEW.region_code, 'br')
                ORDER BY recorded_at DESC
                LIMIT 1;

                IF last_recorded_price IS NOT NULL THEN
                    -- Record if price change is significant (>5%) or if it's been more than 7 days
                    IF ABS(new_price_cents - last_recorded_price) >= (last_recorded_price * 0.05) 
                       OR NOT EXISTS (
                           SELECT 1 FROM steam_price_history 
                           WHERE game_id = NEW.game_id 
                               AND region_code = COALESCE(NEW.region_code, 'br')
                               AND recorded_at >= NOW() - INTERVAL '7 days'
                       ) THEN
                        should_record := TRUE;
                        record_type := 'significant';
                    END IF;
                END IF;
            END;
        END IF;

        -- Record the price if it meets our criteria
        IF should_record THEN
            INSERT INTO steam_price_history (
                game_id,
                region_code,
                price_cents,
                currency,
                original_price_cents,
                discount_percentage,
                recorded_at,
                price_type
            ) VALUES (
                NEW.game_id,
                COALESCE(NEW.region_code, 'br'),
                new_price_cents,
                NEW.currency,
                -- Convert original price to cents if exists
                CASE 
                    WHEN NEW.original_price IS NOT NULL THEN
                        CASE 
                            WHEN NEW.currency = 'BRL' THEN 
                                CAST(REPLACE(REPLACE(NEW.original_price, 'R$ ', ''), ',', '.') AS DECIMAL) * 100
                            WHEN NEW.currency = 'USD' THEN 
                                CAST(REPLACE(NEW.original_price, '$', '') AS DECIMAL) * 100
                            WHEN NEW.currency = 'EUR' THEN 
                                CAST(REPLACE(REPLACE(NEW.original_price, '€', ''), ',', '.') AS DECIMAL) * 100
                            WHEN NEW.currency = 'GBP' THEN 
                                CAST(REPLACE(NEW.original_price, '£', '') AS DECIMAL) * 100
                            WHEN NEW.currency = 'JPY' THEN 
                                CAST(REPLACE(NEW.original_price, '¥', '') AS DECIMAL) * 100
                            WHEN NEW.currency = 'CAD' THEN 
                                CAST(REPLACE(NEW.original_price, 'CDN$ ', '') AS DECIMAL) * 100
                            WHEN NEW.currency = 'AUD' THEN 
                                CAST(REPLACE(NEW.original_price, 'A$ ', '') AS DECIMAL) * 100
                            ELSE 
                                CAST(REGEXP_REPLACE(NEW.original_price, '[^0-9.]', '', 'g') AS DECIMAL) * 100
                        END
                    ELSE NULL
                END,
                NEW.discount_percentage,
                NOW(),
                record_type
            )
            -- Handle conflicts gracefully
            ON CONFLICT (game_id, region_code, recorded_at) DO UPDATE SET
                price_cents = EXCLUDED.price_cents,
                original_price_cents = EXCLUDED.original_price_cents,
                discount_percentage = EXCLUDED.discount_percentage,
                price_type = EXCLUDED.price_type;
                
            RAISE NOTICE 'Steam price archived: % for game % in region % (type: %)', 
                NEW.price, NEW.game_id, COALESCE(NEW.region_code, 'br'), record_type;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 5. Create trigger to automatically archive Steam prices
CREATE TRIGGER trigger_archive_steam_price_history
    AFTER INSERT OR UPDATE ON game_prices
    FOR EACH ROW
    EXECUTE FUNCTION archive_steam_price_to_history();

-- 6. Update the chart function to include price_type
CREATE OR REPLACE FUNCTION get_steam_price_history_for_chart(
    p_game_id UUID, 
    p_region_code VARCHAR(5) DEFAULT 'br',
    p_days_back INTEGER DEFAULT 90
)
RETURNS TABLE (
    price_cents INTEGER,
    currency VARCHAR(10),
    recorded_at TIMESTAMP WITH TIME ZONE,
    discount_percentage INTEGER,
    original_price_cents INTEGER,
    price_type VARCHAR(20)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        sph.price_cents,
        sph.currency,
        sph.recorded_at,
        sph.discount_percentage,
        sph.original_price_cents,
        COALESCE(sph.price_type, 'regular') as price_type
    FROM steam_price_history sph
    WHERE sph.game_id = p_game_id 
        AND sph.region_code = p_region_code
        AND sph.recorded_at >= NOW() - INTERVAL '1 day' * p_days_back
    ORDER BY sph.recorded_at ASC;
END;
$$ LANGUAGE plpgsql;

-- 7. Test the setup with a simple query
SELECT 'Steam price history system updated successfully' as status;