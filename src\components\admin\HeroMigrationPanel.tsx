/**
 * Admin panel for managing SteamGridDB hero banner migration
 */

'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useHeroMigration } from '@/hooks/useHeroMigration';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { 
  Play, 
  RefreshCw, 
  BarChart3, 
  AlertCircle, 
  CheckCircle, 
  Clock,
  Database,
  Zap
} from 'lucide-react';

export default function HeroMigrationPanel() {
  const {
    stats,
    loading,
    error,
    migrationInProgress,
    getStats,
    startMigration,
    processSingleGame,
    resetGames,
    clearError
  } = useHeroMigration();

  const [gameIdInput, setGameIdInput] = useState('');
  const [batchSize, setBatchSize] = useState(10);
  const [maxConcurrent, setMaxConcurrent] = useState(3);

  const handleStartMigration = async () => {
    await startMigration({
      batchSize,
      maxConcurrent,
      delayBetweenBatches: 2000,
      skipExisting: true
    });
  };

  const handleProcessSingle = async () => {
    if (!gameIdInput.trim()) return;
    
    const success = await processSingleGame(gameIdInput.trim());
    if (success) {
      setGameIdInput('');
    }
  };

  const handleResetAll = async () => {
    if (confirm('Are you sure you want to reset all hero cache statuses? This will mark all games for re-processing.')) {
      await resetGames();
    }
  };

  const getProgressPercentage = () => {
    if (!stats || stats.totalGames === 0) return 0;
    return Math.round((stats.processed / stats.totalGames) * 100);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">
            <span className="text-violet-400">//</span> Hero Banner Migration
          </h2>
          <p className="text-slate-400 mt-1">
            Manage SteamGridDB hero banner processing for existing games
          </p>
        </div>
        
        <Button
          onClick={getStats}
          disabled={loading}
          variant="outline"
          size="sm"
          className="border-slate-600 text-slate-300"
        >
          <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh Stats
        </Button>
      </div>

      {/* Error Display */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-red-500/10 border border-red-500/30 rounded-lg p-4"
        >
          <div className="flex items-center gap-2">
            <AlertCircle className="w-5 h-5 text-red-400" />
            <span className="text-red-300 font-medium">Error</span>
          </div>
          <p className="text-red-200 mt-1 text-sm">{error}</p>
          <Button
            onClick={clearError}
            variant="ghost"
            size="sm"
            className="mt-2 text-red-300 hover:text-red-200"
          >
            Dismiss
          </Button>
        </motion.div>
      )}

      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="bg-slate-800/40 border-slate-700/50 p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-500/20 rounded-lg">
                <Database className="w-5 h-5 text-blue-400" />
              </div>
              <div>
                <p className="text-slate-400 text-sm">Total Games</p>
                <p className="text-white text-xl font-bold">{stats.totalGames.toLocaleString()}</p>
              </div>
            </div>
          </Card>

          <Card className="bg-slate-800/40 border-slate-700/50 p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-500/20 rounded-lg">
                <CheckCircle className="w-5 h-5 text-green-400" />
              </div>
              <div>
                <p className="text-slate-400 text-sm">Successful</p>
                <p className="text-white text-xl font-bold">{stats.successful.toLocaleString()}</p>
              </div>
            </div>
          </Card>

          <Card className="bg-slate-800/40 border-slate-700/50 p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-500/20 rounded-lg">
                <Clock className="w-5 h-5 text-yellow-400" />
              </div>
              <div>
                <p className="text-slate-400 text-sm">In Progress</p>
                <p className="text-white text-xl font-bold">{stats.inProgress.toLocaleString()}</p>
              </div>
            </div>
          </Card>

          <Card className="bg-slate-800/40 border-slate-700/50 p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-red-500/20 rounded-lg">
                <AlertCircle className="w-5 h-5 text-red-400" />
              </div>
              <div>
                <p className="text-slate-400 text-sm">Failed</p>
                <p className="text-white text-xl font-bold">{stats.failed.toLocaleString()}</p>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Progress Bar */}
      {stats && stats.totalGames > 0 && (
        <Card className="bg-slate-800/40 border-slate-700/50 p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-slate-300 font-medium">Migration Progress</span>
            <span className="text-slate-400 text-sm">{getProgressPercentage()}%</span>
          </div>
          <div className="w-full bg-slate-700/50 rounded-full h-2">
            <motion.div
              className="bg-violet-500 h-2 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${getProgressPercentage()}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>
          <div className="flex justify-between text-xs text-slate-500 mt-1">
            <span>{stats.processed.toLocaleString()} processed</span>
            <span>{(stats.totalGames - stats.processed).toLocaleString()} remaining</span>
          </div>
        </Card>
      )}

      {/* Migration Controls */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Bulk Migration */}
        <Card className="bg-slate-800/40 border-slate-700/50 p-6">
          <h3 className="text-lg font-semibold text-white mb-4">
            <span className="text-violet-400">//</span> Bulk Migration
          </h3>
          
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm text-slate-400 mb-1">Batch Size</label>
                <input
                  type="number"
                  value={batchSize}
                  onChange={(e) => setBatchSize(Number(e.target.value))}
                  min="1"
                  max="50"
                  className="w-full px-3 py-2 bg-slate-700/50 border border-slate-600/50 rounded-md text-white text-sm"
                />
              </div>
              <div>
                <label className="block text-sm text-slate-400 mb-1">Max Concurrent</label>
                <input
                  type="number"
                  value={maxConcurrent}
                  onChange={(e) => setMaxConcurrent(Number(e.target.value))}
                  min="1"
                  max="10"
                  className="w-full px-3 py-2 bg-slate-700/50 border border-slate-600/50 rounded-md text-white text-sm"
                />
              </div>
            </div>

            <Button
              onClick={handleStartMigration}
              disabled={loading || migrationInProgress}
              className="w-full bg-violet-600 hover:bg-violet-700 text-white"
            >
              {migrationInProgress ? (
                <>
                  <Clock className="w-4 h-4 mr-2 animate-pulse" />
                  Migration In Progress...
                </>
              ) : (
                <>
                  <Play className="w-4 h-4 mr-2" />
                  Start Migration
                </>
              )}
            </Button>
          </div>
        </Card>

        {/* Single Game Processing */}
        <Card className="bg-slate-800/40 border-slate-700/50 p-6">
          <h3 className="text-lg font-semibold text-white mb-4">
            <span className="text-violet-400">//</span> Single Game
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm text-slate-400 mb-1">Game ID</label>
              <input
                type="text"
                value={gameIdInput}
                onChange={(e) => setGameIdInput(e.target.value)}
                placeholder="Enter game UUID..."
                className="w-full px-3 py-2 bg-slate-700/50 border border-slate-600/50 rounded-md text-white text-sm"
              />
            </div>

            <Button
              onClick={handleProcessSingle}
              disabled={loading || !gameIdInput.trim()}
              className="w-full bg-green-600 hover:bg-green-700 text-white"
            >
              <Zap className="w-4 h-4 mr-2" />
              Process Single Game
            </Button>
          </div>
        </Card>
      </div>

      {/* Danger Zone */}
      <Card className="bg-red-500/5 border-red-500/30 p-6">
        <h3 className="text-lg font-semibold text-red-300 mb-4">
          <span className="text-red-400">//</span> Danger Zone
        </h3>
        
        <div className="space-y-4">
          <p className="text-red-200 text-sm">
            Reset all hero cache statuses. This will mark all games for re-processing.
          </p>
          
          <Button
            onClick={handleResetAll}
            disabled={loading}
            variant="destructive"
            className="bg-red-600 hover:bg-red-700"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Reset All Hero Cache
          </Button>
        </div>
      </Card>
    </div>
  );
}
