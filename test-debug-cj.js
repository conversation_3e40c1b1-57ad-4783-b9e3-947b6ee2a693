/**
 * Debug script to test CJ API directly
 * This will help identify why Gamivo/Fanatical aren't showing up
 */

console.log('🔍 DEBUG: Testing CJ API functions directly...\n');

// Test script to debug CJ API with new product-search endpoints

const testCJAPI = async () => {
  const config = {
    developerKey: process.env.CJ_DEVELOPER_KEY || 'your-key-here',
    baseUrl: 'https://api.cj.com',
    timeout: 10000
  };

  if (!config.developerKey || config.developerKey === 'your-key-here') {
    console.log('❌ CJ_DEVELOPER_KEY não configurado no .env.local');
    return;
  }

  console.log('🔧 Testing CJ API Product Search Endpoints...');
  console.log(`📋 Developer Key: ${config.developerKey.substring(0, 8)}...`);

  // Updated endpoints for product search
  const endpoints = [
    `${config.baseUrl}/v3/product-search`,
    `${config.baseUrl}/v2/product-search`,
    `${config.baseUrl}/v3/product-catalog`,
    `${config.baseUrl}/v2/product-catalog`,
    'https://product-search.api.cj.com/v2/product-search',
    'https://product-catalog.api.cj.com/v2/product-catalog'
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`\n🔍 Testing: ${endpoint}`);
      
      const url = `${endpoint}?keywords=${encodeURIComponent('Elden Ring')}&records-per-page=5&advertiser-ids=joined`;
      console.log(`📡 Full URL: ${url}`);
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${config.developerKey}`,
          'Accept': 'application/xml',
          'User-Agent': 'CriticalPixel/1.0',
          'Content-Type': 'application/xml'
        },
        signal: AbortSignal.timeout(config.timeout)
      });

      console.log(`📊 Status: ${response.status} ${response.statusText}`);
      console.log(`📋 Headers: ${JSON.stringify(Object.fromEntries(response.headers.entries()))}`);

      const responseText = await response.text();
      console.log(`📄 Response Length: ${responseText.length} chars`);
      console.log(`📄 Response Preview: ${responseText.substring(0, 300)}...`);

      if (response.status === 200) {
        if (responseText.includes('<?xml')) {
          console.log('✅ SUCCESS: Received XML data!');
          
          // Try to find product elements
          const productMatches = responseText.match(/<product[^>]*>[\s\S]*?<\/product>/gi) ||
                                responseText.match(/<item[^>]*>[\s\S]*?<\/item>/gi);
          
          if (productMatches) {
            console.log(`🎯 Found ${productMatches.length} product elements`);
            console.log(`🔍 First product: ${productMatches[0].substring(0, 200)}...`);
          } else {
            console.log('⚠️ No product elements found in XML');
          }
          
          break; // Found working endpoint
        } else {
          console.log('⚠️ Received non-XML response (probably HTML)');
        }
      } else if (response.status === 401) {
        console.log('❌ UNAUTHORIZED - Check your CJ_DEVELOPER_KEY');
      } else if (response.status === 400) {
        console.log('⚠️ BAD REQUEST - Endpoint exists but parameters may be wrong');
        console.log(`📄 Error details: ${responseText.substring(0, 500)}`);
      }

    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
    }
  }

  console.log('\n🏁 CJ API Test Complete');
};

// Run the test
testCJAPI().catch(console.error); 