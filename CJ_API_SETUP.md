# 🔧 CJ API Setup Guide - Endpoints Atualizados

## ✅ Configuração Corrigida (Janeiro 2025)

### **Problema Identificado:**
A CJ API estava retornando **HTML** ao invés de **XML** porque estávamos usando endpoints incorretos (`advertiser-lookup` ao invés de `product-search`).

### **✅ Endpoints Corretos Implementados:**
```
https://api.cj.com/v3/product-search
https://api.cj.com/v2/product-search
https://api.cj.com/v3/product-catalog
https://api.cj.com/v2/product-catalog
https://product-search.api.cj.com/v2/product-search
https://product-catalog.api.cj.com/v2/product-catalog
```

### **📋 Parâmetros Corretos:**
- `keywords` - Termo de busca (nome do jogo)
- `records-per-page` - Número de resultados (máx 50)
- `advertiser-ids=joined` - Apenas anunciantes aprovados

### **🔑 Variáveis de Ambiente (.env.local):**
```bash
# CJ Affiliate API - OBRIGATÓRIO
CJ_DEVELOPER_KEY=sua_chave_aqui
CJ_WEBSITE_ID=seu_website_id
CJ_COMPANY_ID=seu_company_id
CJ_FORCE_REAL_DATA=true

# Outras configurações existentes...
KINGUIN_API_KEY=sua_chave_kinguin
KINGUIN_AFFILIATE_ID=seu_affiliate_id
```

### **🎯 Como Obter Credenciais CJ:**

1. **Acesse:** https://developers.cj.com/
2. **Login** na sua conta CJ Affiliate
3. **Developer Portal** → API Settings
4. **Copy:**
   - Developer Key (API Key)
   - Website ID
   - Company ID

### **🧪 Testar Configuração:**

1. **Configure .env.local** com suas credenciais
2. **Restart server:** `npm run dev`
3. **Abra qualquer jogo** (ex: Elden Ring)
4. **Force Update** no widget de preços
5. **Check browser console** para:

```
✅ Sucesso (XML recebido):
🔍 CJ XML Parser: Processing 5000 characters of XML data
📄 XML Preview: <?xml version="1.0"?>...
✅ CJ XML Parser: Product 1: Elden Ring - USD 29.99
💰 CJ/Gamivo: Preço encontrado USD 29.99
```

```
❌ Ainda HTML (credenciais incorretas):
📄 XML Preview: <!doctype html><html...
⚠️ CJ String Parser: No product blocks found in XML
❌ CJ/Gamivo API returned null
```

### **🔍 Debug Manual:**
Execute o script de teste:
```bash
node test-debug-cj.js
```

### **📊 Resultado Esperado:**
- **Steam** ✅ (já funcionando)
- **Kinguin** ✅ (já funcionando)  
- **Gamivo** 🔥 (agora com CJ API real)
- **Fanatical** 🔥 (agora com CJ API real)

### **🚨 Troubleshooting:**

**Problema:** Ainda retorna HTML
**Solução:** Verificar credenciais CJ e status da conta

**Problema:** XML vazio
**Solução:** Aguardar aprovação dos programas Gamivo/Fanatical

**Problema:** Erro 401 Unauthorized
**Solução:** Verificar CJ_DEVELOPER_KEY

**Problema:** Erro 400 Bad Request
**Solução:** Endpoint funciona, mas parâmetros podem estar incorretos

### **✅ Status da Implementação:**
- [x] Parser XML completo implementado
- [x] Endpoints corretos configurados
- [x] Parâmetros de product-search corretos
- [x] Headers corretos (Authorization, Accept, Content-Type)
- [x] Fallback para string parsing
- [x] Error handling robusto
- [x] Debug logging detalhado

**🎯 Próximo passo:** Configurar credenciais reais e testar! 