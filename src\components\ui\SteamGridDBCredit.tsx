/**
 * SteamGridDB Credit Component
 * Displays attribution for SteamGridDB and banner authors
 */

import React from 'react';
import { ExternalLink, User } from 'lucide-react';
import { HeroCredits } from '@/hooks/useSteamGridDBHero';

interface SteamGridDBCreditProps {
  credits: HeroCredits;
  className?: string;
  variant?: 'default' | 'compact' | 'overlay';
}

export default function SteamGridDBCredit({ 
  credits, 
  className = '',
  variant = 'default'
}: SteamGridDBCreditProps) {
  const steamGridDBUrl = `https://www.steamgriddb.com/game/${credits.gameId}`;
  const authorProfileUrl = credits.authorSteam64 
    ? `https://steamcommunity.com/profiles/${credits.authorSteam64}`
    : null;

  if (variant === 'compact') {
    return (
      <div className={`flex items-center gap-2 text-xs text-slate-400 ${className}`}>
        <span>Banner by</span>
        <a
          href={steamGridDBUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="text-violet-400 hover:text-violet-300 transition-colors flex items-center gap-1"
        >
          SteamGridDB
          <ExternalLink className="w-3 h-3" />
        </a>
      </div>
    );
  }

  if (variant === 'overlay') {
    return (
      <div className={`absolute bottom-2 right-2 bg-black/60 backdrop-blur-sm rounded-md px-2 py-1 ${className}`}>
        <div className="flex items-center gap-2 text-xs text-white/80">
          <span>via</span>
          <a
            href={steamGridDBUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="text-violet-300 hover:text-violet-200 transition-colors flex items-center gap-1"
          >
            SteamGridDB
            <ExternalLink className="w-3 h-3" />
          </a>
        </div>
      </div>
    );
  }

  // Default variant
  return (
    <div className={`bg-slate-800/40 border border-slate-700/50 rounded-lg p-3 ${className}`}>
      <div className="flex items-center gap-3">
        <div className="flex-shrink-0">
          <div className="w-8 h-8 bg-violet-600/20 rounded-lg flex items-center justify-center">
            <svg 
              className="w-4 h-4 text-violet-400" 
              viewBox="0 0 24 24" 
              fill="currentColor"
            >
              <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
            </svg>
          </div>
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <span className="text-xs text-slate-400 uppercase tracking-wider font-mono">
              Banner Source
            </span>
          </div>
          
          <div className="flex items-center gap-2">
            <a
              href={steamGridDBUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="text-sm font-medium text-violet-300 hover:text-violet-200 transition-colors flex items-center gap-1"
            >
              SteamGridDB
              <ExternalLink className="w-3 h-3" />
            </a>
            
            {credits.authorName && (
              <>
                <span className="text-slate-500">•</span>
                <div className="flex items-center gap-1">
                  <User className="w-3 h-3 text-slate-500" />
                  {authorProfileUrl ? (
                    <a
                      href={authorProfileUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm text-slate-300 hover:text-slate-200 transition-colors"
                    >
                      {credits.authorName}
                    </a>
                  ) : (
                    <span className="text-sm text-slate-300">
                      {credits.authorName}
                    </span>
                  )}
                </div>
              </>
            )}
          </div>
          
          {credits.authorAvatar && (
            <div className="mt-2 flex items-center gap-2">
              <img
                src={credits.authorAvatar}
                alt={`${credits.authorName} avatar`}
                className="w-6 h-6 rounded-full border border-slate-600"
              />
              <span className="text-xs text-slate-500">
                Created by {credits.authorName}
              </span>
            </div>
          )}
        </div>
      </div>
      
      <div className="mt-3 pt-3 border-t border-slate-700/50">
        <div className="flex items-center justify-between text-xs text-slate-500">
          <span>Hero ID: {credits.heroId}</span>
          <span>Game ID: {credits.gameId}</span>
        </div>
      </div>
    </div>
  );
}
