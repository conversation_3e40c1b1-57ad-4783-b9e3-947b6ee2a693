import { createClient } from '@/lib/supabase/client';

export interface SteamPriceHistoryPoint {
  id: string;
  price_cents: number;
  currency: string;
  recorded_at: string;
  discount_percentage?: number;
  original_price_cents?: number;
}

export interface SteamPriceHistoryData {
  gameId: string;
  regionCode: string;
  history: SteamPriceHistoryPoint[];
  lowestPrice?: {
    price_cents: number;
    currency: string;
    recorded_at: string;
  };
  currentPrice?: {
    price_cents: number;
    currency: string;
  };
}

export class SteamPriceHistoryService {
  /**
   * Get Steam price history for a game in a specific region
   */
  static async getPriceHistory(
    gameId: string, 
    regionCode: string = 'br', 
    daysBack: number = 90
  ): Promise<SteamPriceHistoryData | null> {
    try {
      const supabase = createClient();
      
      // Get price history using the database function
      const { data: historyData, error: historyError } = await supabase
        .rpc('get_steam_price_history_for_chart', {
          p_game_id: gameId,
          p_region_code: regionCode,
          p_days_back: daysBack
        });

      if (historyError) {
        console.error('Error fetching Steam price history:', historyError);
        return null;
      }

      if (!historyData || historyData.length === 0) {
        console.log(`No price history found for game ${gameId} in region ${regionCode}`);
        return {
          gameId,
          regionCode,
          history: []
        };
      }

      // Get lowest price ever
      const { data: lowestPriceData, error: lowestError } = await supabase
        .rpc('get_lowest_steam_price', {
          p_game_id: gameId,
          p_region_code: regionCode
        });

      if (lowestError) {
        console.error('Error fetching lowest Steam price:', lowestError);
      }

      // Get current price from game_prices table
      const { data: currentPriceData, error: currentError } = await supabase
        .from('game_prices')
        .select('price, currency')
        .eq('game_id', gameId)
        .eq('store_name', 'Steam')
        .eq('region_code', regionCode)
        .single();

      if (currentError && currentError.code !== 'PGRST116') {
        console.error('Error fetching current Steam price:', currentError);
      }

      // Convert current price string to cents for comparison
      let currentPriceCents: number | undefined;
      if (currentPriceData?.price) {
        currentPriceCents = this.convertPriceStringToCents(
          currentPriceData.price, 
          currentPriceData.currency
        );
      }

      return {
        gameId,
        regionCode,
        history: historyData.map(point => ({
          id: `${gameId}-${regionCode}-${point.recorded_at}`,
          price_cents: point.price_cents,
          currency: point.currency,
          recorded_at: point.recorded_at,
          discount_percentage: point.discount_percentage,
          original_price_cents: point.original_price_cents
        })),
        lowestPrice: lowestPriceData?.[0] ? {
          price_cents: lowestPriceData[0].lowest_price_cents,
          currency: lowestPriceData[0].currency,
          recorded_at: lowestPriceData[0].recorded_at
        } : undefined,
        currentPrice: currentPriceCents ? {
          price_cents: currentPriceCents,
          currency: currentPriceData.currency
        } : undefined
      };
    } catch (error) {
      console.error('Error in getPriceHistory:', error);
      return null;
    }
  }

  /**
   * Manually record a Steam price to history (for testing or manual updates)
   */
  static async recordPriceToHistory(
    gameId: string,
    regionCode: string,
    priceCents: number,
    currency: string,
    discountPercentage?: number,
    originalPriceCents?: number,
    steamAppId?: string
  ): Promise<boolean> {
    try {
      const supabase = createClient();
      
      const { error } = await supabase
        .from('steam_price_history')
        .insert({
          game_id: gameId,
          region_code: regionCode,
          price_cents: priceCents,
          currency,
          discount_percentage: discountPercentage,
          original_price_cents: originalPriceCents,
          steam_app_id: steamAppId,
          recorded_at: new Date().toISOString()
        });

      if (error) {
        console.error('Error recording price to history:', error);
        return false;
      }

      console.log(`✅ Recorded Steam price to history: ${gameId} - ${priceCents} cents (${currency}) in ${regionCode}`);
      return true;
    } catch (error) {
      console.error('Error in recordPriceToHistory:', error);
      return false;
    }
  }

  /**
   * Get price statistics for a game
   */
  static async getPriceStatistics(
    gameId: string, 
    regionCode: string = 'br'
  ): Promise<{
    lowest: number;
    highest: number;
    average: number;
    current?: number;
    currency: string;
    totalDataPoints: number;
  } | null> {
    try {
      const historyData = await this.getPriceHistory(gameId, regionCode, 365); // Last year
      
      if (!historyData || historyData.history.length === 0) {
        return null;
      }

      const prices = historyData.history.map(h => h.price_cents);
      const lowest = Math.min(...prices);
      const highest = Math.max(...prices);
      const average = Math.round(prices.reduce((sum, price) => sum + price, 0) / prices.length);
      const currency = historyData.history[0]?.currency || 'BRL';

      return {
        lowest,
        highest,
        average,
        current: historyData.currentPrice?.price_cents,
        currency,
        totalDataPoints: prices.length
      };
    } catch (error) {
      console.error('Error getting price statistics:', error);
      return null;
    }
  }

  /**
   * Convert price string to cents for calculations
   */
  private static convertPriceStringToCents(priceString: string, currency: string): number {
    try {
      let cleanPrice: string;
      
      switch (currency) {
        case 'BRL':
          cleanPrice = priceString.replace('R$ ', '').replace(',', '.');
          break;
        case 'USD':
          cleanPrice = priceString.replace('$', '');
          break;
        case 'EUR':
          cleanPrice = priceString.replace('€', '');
          break;
        default:
          cleanPrice = priceString.replace(/[^0-9.]/g, '');
      }
      
      return Math.round(parseFloat(cleanPrice) * 100);
    } catch (error) {
      console.error('Error converting price string to cents:', error);
      return 0;
    }
  }

  /**
   * Convert cents to formatted price string
   */
  static formatPrice(priceCents: number, currency: string): string {
    const price = priceCents / 100;
    
    switch (currency) {
      case 'BRL':
        return `R$ ${price.toFixed(2).replace('.', ',')}`;
      case 'USD':
        return `$${price.toFixed(2)}`;
      case 'EUR':
        return `€${price.toFixed(2)}`;
      case 'GBP':
        return `£${price.toFixed(2)}`;
      case 'JPY':
        return `¥${Math.round(price)}`;
      default:
        return `${price.toFixed(2)} ${currency}`;
    }
  }

  /**
   * Check if price history exists for a game
   */
  static async hasPriceHistory(gameId: string, regionCode: string = 'br'): Promise<boolean> {
    try {
      const supabase = createClient();
      
      const { data, error } = await supabase
        .from('steam_price_history')
        .select('id')
        .eq('game_id', gameId)
        .eq('region_code', regionCode)
        .limit(1);

      if (error) {
        console.error('Error checking price history existence:', error);
        return false;
      }

      return data && data.length > 0;
    } catch (error) {
      console.error('Error in hasPriceHistory:', error);
      return false;
    }
  }

  /**
   * Get the price change percentage from lowest to current
   */
  static calculatePriceChangeFromLowest(
    currentPriceCents: number, 
    lowestPriceCents: number
  ): number {
    if (lowestPriceCents === 0) return 0;
    return Math.round(((currentPriceCents - lowestPriceCents) / lowestPriceCents) * 100);
  }

  /**
   * Generate sample price history data for testing (development only)
   */
  static async generateSampleData(
    gameId: string,
    regionCode: string = 'br',
    daysBack: number = 90
  ): Promise<boolean> {
    if (process.env.NODE_ENV === 'production') {
      console.error('Sample data generation is not allowed in production');
      return false;
    }

    try {
      const supabase = createClient();
      const now = new Date();
      const basePrice = 5999; // 59.99 in cents
      const records = [];

      for (let i = daysBack; i >= 0; i -= 7) { // Weekly samples
        const date = new Date(now);
        date.setDate(date.getDate() - i);
        
        // Generate realistic price variations
        const variation = (Math.random() - 0.5) * 0.4; // ±20% variation
        const discountChance = Math.random();
        let price = Math.round(basePrice * (1 + variation));
        let originalPrice = null;
        let discountPercentage = null;

        // 30% chance of discount
        if (discountChance < 0.3) {
          originalPrice = price;
          discountPercentage = Math.floor(Math.random() * 50) + 10; // 10-60% discount
          price = Math.round(originalPrice * (1 - discountPercentage / 100));
        }

        records.push({
          game_id: gameId,
          region_code: regionCode,
          price_cents: price,
          currency: regionCode === 'br' ? 'BRL' : 'USD',
          original_price_cents: originalPrice,
          discount_percentage: discountPercentage,
          recorded_at: date.toISOString()
        });
      }

      const { error } = await supabase
        .from('steam_price_history')
        .insert(records);

      if (error) {
        console.error('Error generating sample data:', error);
        return false;
      }

      console.log(`✅ Generated ${records.length} sample price history records for game ${gameId}`);
      return true;
    } catch (error) {
      console.error('Error in generateSampleData:', error);
      return false;
    }
  }
}