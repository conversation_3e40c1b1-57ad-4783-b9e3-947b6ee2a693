# 🚀 CJ API - Configuração para Dados Reais (Gamivo + Afiliados)

## ✅ **Problema Resolvido**

Você estava recebendo dados mock (fake) e links incorretos porque o sistema estava em modo desenvolvimento. Agora está configurado para usar dados reais da CJ API.

## 🔧 **Como Ativar Dados Reais**

### 1. Configure suas credenciais no `.env.local`:

```bash
# Set to 'true' to use real CJ API data instead of mock data
CJ_FORCE_REAL_DATA=true

# Your CJ API Credentials
CJ_DEVELOPER_KEY=your_cj_developer_key_here
CJ_WEBSITE_ID=your_website_id_here
CJ_COMPANY_ID=your_company_id_here

# Optional settings
CJ_API_BASE_URL=https://api.cj.com
CJ_REQUEST_TIMEOUT=30000
```

### 2. Reinicie o servidor de desenvolvimento:

```bash
npm run dev
```

## 🎯 **O que mudou**

### ✅ **Antes (Mock Data):**
- ❌ Preços fake gerados automaticamente
- ❌ Links diretos para busca genérica
- ❌ Sem rastreamento de afiliado

### ✅ **Agora (Real Data):**
- ✅ Preços reais via CJ API
- ✅ Links de produto específicos
- ✅ URLs de afiliado com rastreamento CJ
- ✅ Gamivo integrado via CJ

## 🔍 **Como Verificar se Está Funcionando**

### No Console do Browser (F12):
```
🔥 CJ API: FORCED REAL DATA MODE - Mock data disabled, using live API calls
🔥 REAL DATA MODE: Attempting real CJ API call for "Cyberpunk 2077"
💰 CJ/Gamivo: Preço encontrado USD 19.99 para "Cyberpunk 2077"
🔗 AFFILIATE URL: https://www.jdoqocy.com/click-YOURSITE-123?url=...
```

### Indicadores de Sucesso:
- ✅ Preços diferentes a cada busca (não sempre os mesmos)
- ✅ URLs começando com `jdoqocy.com`, `anrdoezrs.net` ou `dpbolvw.net`
- ✅ Gamivo aparece no widget de preços
- ✅ Links levam para produtos específicos, não busca genérica

## 🛠 **Teste Rápido**

1. Abra uma página de jogo qualquer
2. Abra o console (F12)
3. Procure por mensagens como:
   - `🔥 CJ API: FORCED REAL DATA MODE`
   - `💰 CJ/Gamivo: Preço encontrado`
   - `🔗 AFFILIATE URL:`

## 📊 **Stores Integradas via CJ**

- **Gamivo** ✅ (Recém implementado)
- **Fanatical** ✅ 
- **Humble Bundle** ✅
- **GamesPlanet** ✅
- **Green Man Gaming** ✅
- **CDKeys** ✅
- **Voidu** ✅
- **Eneba** ✅

## ⚠️ **Importante**

- Se `CJ_FORCE_REAL_DATA=false` ou não configurado: Usa dados mock
- Se `CJ_FORCE_REAL_DATA=true`: Usa dados reais da CJ API
- Credenciais inválidas: Sistema mostra erro no console

## 🎮 **Resultado Final**

O Gamivo agora aparece no GamePricesWidget com:
- ✅ Preços reais via CJ API
- ✅ Links de afiliado funcionais
- ✅ Rastreamento correto de comissões
- ✅ Integração completa com seu programa de afiliados CJ 