// Simple test to verify SteamGridDB integration
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Test the API endpoint
async function testAPI() {
  try {
    console.log('🧪 Testing SteamGridDB API integration...\n');
    
    const response = await fetch('http://localhost:9003/api/steamgriddb/fetch-hero', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        gameId: '3d128ab8-b617-45e5-861e-2d25059c5104',
        gameName: 'Elden Ring'
      }),
    });

    console.log('Response status:', response.status);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Success!');
      console.log('Response:', JSON.stringify(data, null, 2));
    } else {
      const errorText = await response.text();
      console.log('❌ Error response:', errorText);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Import fetch for Node.js
async function main() {
  const { default: fetch } = await import('node-fetch');
  global.fetch = fetch;
  await testAPI();
}

main();
