/**
 * React hook for managing SteamGridDB hero banner migration
 */

import { useState, useCallback, useEffect } from 'react';

export interface MigrationStats {
  totalGames: number;
  processed: number;
  successful: number;
  failed: number;
  skipped: number;
  inProgress: number;
}

export interface MigrationOptions {
  batchSize?: number;
  delayBetweenBatches?: number;
  maxConcurrent?: number;
  skipExisting?: boolean;
  gameIds?: string[];
}

export interface UseHeroMigrationResult {
  stats: MigrationStats | null;
  loading: boolean;
  error: string | null;
  migrationInProgress: boolean;
  
  // Actions
  getStats: () => Promise<void>;
  startMigration: (options?: MigrationOptions) => Promise<void>;
  processSingleGame: (gameId: string) => Promise<boolean>;
  resetGames: (gameIds?: string[]) => Promise<number>;
  checkNeedsProcessing: (gameId: string) => Promise<boolean>;
  clearError: () => void;
}

export function useHeroMigration(): UseHeroMigrationResult {
  const [stats, setStats] = useState<MigrationStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [migrationInProgress, setMigrationInProgress] = useState(false);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const getStats = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/admin/migrate-heroes');
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success) {
        setStats(data.data);
      } else {
        throw new Error(data.error || 'Failed to get migration stats');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('Failed to get migration stats:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const startMigration = useCallback(async (options: MigrationOptions = {}) => {
    setLoading(true);
    setError(null);
    setMigrationInProgress(true);

    try {
      const response = await fetch('/api/admin/migrate-heroes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'migrate',
          ...options
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to start migration');
      }

      // Refresh stats after starting migration
      setTimeout(() => {
        getStats();
      }, 1000);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      setMigrationInProgress(false);
      console.error('Failed to start migration:', err);
    } finally {
      setLoading(false);
    }
  }, [getStats]);

  const processSingleGame = useCallback(async (gameId: string): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/admin/migrate-heroes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'process-single',
          gameId
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success) {
        // Refresh stats after processing
        getStats();
        return true;
      } else {
        throw new Error(data.message || 'Failed to process game');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('Failed to process single game:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [getStats]);

  const resetGames = useCallback(async (gameIds?: string[]): Promise<number> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/admin/migrate-heroes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'reset',
          gameIds
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success) {
        // Refresh stats after reset
        getStats();
        return data.data.resetCount;
      } else {
        throw new Error(data.error || 'Failed to reset games');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('Failed to reset games:', err);
      return 0;
    } finally {
      setLoading(false);
    }
  }, [getStats]);

  const checkNeedsProcessing = useCallback(async (gameId: string): Promise<boolean> => {
    try {
      const response = await fetch('/api/admin/migrate-heroes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'check-needs-processing',
          gameId
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success) {
        return data.data.needsProcessing;
      } else {
        throw new Error(data.error || 'Failed to check processing status');
      }
    } catch (err) {
      console.error('Failed to check needs processing:', err);
      return true; // Default to needing processing on error
    }
  }, []);

  // Auto-refresh stats periodically when migration is in progress
  useEffect(() => {
    if (migrationInProgress) {
      const interval = setInterval(() => {
        getStats().then(() => {
          // Check if migration is complete by looking at inProgress count
          if (stats && stats.inProgress === 0) {
            setMigrationInProgress(false);
          }
        });
      }, 5000); // Refresh every 5 seconds

      return () => clearInterval(interval);
    }
  }, [migrationInProgress, getStats, stats]);

  // Load initial stats on mount
  useEffect(() => {
    getStats();
  }, [getStats]);

  return {
    stats,
    loading,
    error,
    migrationInProgress,
    getStats,
    startMigration,
    processSingleGame,
    resetGames,
    checkNeedsProcessing,
    clearError
  };
}
