// Test SteamGridDB search for Elden Ring specifically
const { default: SGDB } = require('steamgriddb');
require('dotenv').config({ path: '.env.local' });

async function testEldenRingSearch() {
  try {
    console.log('🔍 Testing SteamGridDB search for Elden Ring...\n');
    
    const client = new SGDB(process.env.STEAMGRIDDB_API_KEY);
    
    // Try different search variations
    const searchTerms = [
      'Elden Ring',
      'ELDEN RING',
      'elden ring',
      'Elden',
      'Ring'
    ];
    
    for (const term of searchTerms) {
      console.log(`🎮 Searching for: "${term}"`);
      
      try {
        const games = await client.searchGame(term);
        console.log(`   Found ${games.length} games:`);
        
        games.slice(0, 5).forEach((game, index) => {
          console.log(`   ${index + 1}. ${game.name} (ID: ${game.id}) - Verified: ${game.verified}`);
        });
        
        // If we found games, try to get heroes for the first one
        if (games.length > 0) {
          const firstGame = games[0];
          console.log(`\n🎨 Getting heroes for: ${firstGame.name} (ID: ${firstGame.id})`);
          
          try {
            const heroes = await client.getHeroes(firstGame.id, {
              types: ['static'],
              dimensions: ['1920x620', '3840x1240', '1600x650']
            });
            
            console.log(`   Found ${heroes.length} heroes:`);
            heroes.slice(0, 3).forEach((hero, index) => {
              console.log(`   ${index + 1}. ${hero.width}x${hero.height} by ${hero.author?.name || 'Unknown'} (Score: ${hero.score})`);
              console.log(`      URL: ${hero.url}`);
            });
            
            if (heroes.length > 0) {
              console.log(`\n✅ SUCCESS! Found ${heroes.length} heroes for "${term}"`);
              return { searchTerm: term, game: firstGame, heroes };
            }
          } catch (heroError) {
            console.log(`   ❌ Error getting heroes: ${heroError.message}`);
          }
        }
        
        console.log('');
      } catch (searchError) {
        console.log(`   ❌ Search error: ${searchError.message}\n`);
      }
    }
    
    console.log('❌ No heroes found for any search term');
    return null;
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    return null;
  }
}

// Run the test
testEldenRingSearch().then(result => {
  if (result) {
    console.log('\n🎉 Test completed successfully!');
    console.log(`Best match: "${result.searchTerm}" -> ${result.game.name} (${result.heroes.length} heroes)`);
  } else {
    console.log('\n💔 Test failed - no heroes found');
  }
});
