-- Fix Original Price Logic - Run this to fix the trigger
-- This fixes the issue where original prices aren't being stored correctly

DROP TRIGGER IF EXISTS trigger_update_steam_price_tracking ON game_prices;
DROP FUNCTION IF EXISTS update_steam_price_tracking();

-- Create improved function with correct original price logic
CREATE OR REPLACE FUNCTION update_steam_price_tracking()
RETURNS TRIGGER AS $$
DECLARE
    new_price_cents INTEGER;
    tracking_record steam_price_tracking%ROWTYPE;
    needs_update BOOLEAN := FALSE;
    original_price_cents_to_store INTEGER;
BEGIN
    -- Only process Steam prices
    IF NEW.store_name != 'Steam' THEN
        RETURN NEW;
    END IF;

    -- Convert price string to cents
    BEGIN
        new_price_cents := CASE 
            WHEN NEW.currency = 'BRL' THEN 
                CAST(REPLACE(REPLACE(NEW.price, 'R$ ', ''), ',', '.') AS DECIMAL) * 100
            WHEN NEW.currency = 'USD' THEN 
                CAST(REPLACE(NEW.price, '$', '') AS DECIMAL) * 100
            WHEN NEW.currency = 'EUR' THEN 
                CAST(REPLACE(REPLACE(NEW.price, '€', ''), ',', '.') AS DECIMAL) * 100
            WHEN NEW.currency = 'GBP' THEN 
                CAST(REPLACE(NEW.price, '£', '') AS DECIMAL) * 100
            WHEN NEW.currency = 'JPY' THEN 
                CAST(REPLACE(NEW.price, '¥', '') AS DECIMAL) * 100
            ELSE 
                CAST(REGEXP_REPLACE(NEW.price, '[^0-9.]', '', 'g') AS DECIMAL) * 100
        END;
    EXCEPTION
        WHEN OTHERS THEN
            RAISE WARNING 'Failed to parse Steam price: % for currency: %', NEW.price, NEW.currency;
            RETURN NEW;
    END;

    -- Get existing tracking record
    SELECT * INTO tracking_record 
    FROM steam_price_tracking 
    WHERE game_id = NEW.game_id 
        AND region_code = COALESCE(NEW.region_code, 'br')
        AND currency = NEW.currency;

    -- If no record exists, create initial one
    IF tracking_record.id IS NULL THEN
        -- Determine original price for initial record
        IF NEW.original_price IS NOT NULL AND NEW.original_price != '' THEN
            -- Use original price from Steam (before discount)
            original_price_cents_to_store := CASE 
                WHEN NEW.currency = 'BRL' THEN 
                    CAST(REPLACE(REPLACE(NEW.original_price, 'R$ ', ''), ',', '.') AS DECIMAL) * 100
                WHEN NEW.currency = 'USD' THEN 
                    CAST(REPLACE(NEW.original_price, '$', '') AS DECIMAL) * 100
                WHEN NEW.currency = 'EUR' THEN 
                    CAST(REPLACE(REPLACE(NEW.original_price, '€', ''), ',', '.') AS DECIMAL) * 100
                WHEN NEW.currency = 'GBP' THEN 
                    CAST(REPLACE(NEW.original_price, '£', '') AS DECIMAL) * 100
                WHEN NEW.currency = 'JPY' THEN 
                    CAST(REPLACE(NEW.original_price, '¥', '') AS DECIMAL) * 100
                ELSE 
                    CAST(REGEXP_REPLACE(NEW.original_price, '[^0-9.]', '', 'g') AS DECIMAL) * 100
            END;
        ELSE
            -- No discount, current price is original price
            original_price_cents_to_store := new_price_cents;
        END IF;

        INSERT INTO steam_price_tracking (
            game_id, 
            region_code, 
            currency,
            original_price_cents,
            all_time_low_cents,
            all_time_high_cents,
            original_price_updated,
            all_time_low_updated,
            all_time_high_updated
        ) VALUES (
            NEW.game_id,
            COALESCE(NEW.region_code, 'br'),
            NEW.currency,
            original_price_cents_to_store,
            new_price_cents,
            new_price_cents,
            NOW(),
            NOW(),
            NOW()
        );
        RAISE NOTICE 'Steam tracking initialized: game=%, currency=%, original=%, current=%', 
            NEW.game_id, NEW.currency, original_price_cents_to_store, new_price_cents;
        RETURN NEW;
    END IF;

    -- Update existing record - check all 3 datapoints
    
    -- 1. Update original price from Steam's original_price field OR current price
    IF NEW.original_price IS NOT NULL AND NEW.original_price != '' THEN
        -- Steam provides original price (before discount)
        original_price_cents_to_store := CASE 
            WHEN NEW.currency = 'BRL' THEN 
                CAST(REPLACE(REPLACE(NEW.original_price, 'R$ ', ''), ',', '.') AS DECIMAL) * 100
            WHEN NEW.currency = 'USD' THEN 
                CAST(REPLACE(NEW.original_price, '$', '') AS DECIMAL) * 100
            WHEN NEW.currency = 'EUR' THEN 
                CAST(REPLACE(REPLACE(NEW.original_price, '€', ''), ',', '.') AS DECIMAL) * 100
            WHEN NEW.currency = 'GBP' THEN 
                CAST(REPLACE(NEW.original_price, '£', '') AS DECIMAL) * 100
            WHEN NEW.currency = 'JPY' THEN 
                CAST(REPLACE(NEW.original_price, '¥', '') AS DECIMAL) * 100
            ELSE 
                CAST(REGEXP_REPLACE(NEW.original_price, '[^0-9.]', '', 'g') AS DECIMAL) * 100
        END;
        
        -- Always update to the most recent original price from Steam
        IF tracking_record.original_price_cents IS NULL OR 
           tracking_record.original_price_cents != original_price_cents_to_store THEN
            tracking_record.original_price_cents := original_price_cents_to_store;
            tracking_record.original_price_updated := NOW();
            needs_update := TRUE;
        END IF;
    ELSE
        -- No discount, current price might be the original price
        IF tracking_record.original_price_cents IS NULL THEN
            tracking_record.original_price_cents := new_price_cents;
            tracking_record.original_price_updated := NOW();
            needs_update := TRUE;
        END IF;
    END IF;
    
    -- 2. Update all-time low if this is lower
    IF tracking_record.all_time_low_cents IS NULL OR new_price_cents < tracking_record.all_time_low_cents THEN
        tracking_record.all_time_low_cents := new_price_cents;
        tracking_record.all_time_low_updated := NOW();
        needs_update := TRUE;
    END IF;
    
    -- 3. Update all-time high if this is higher
    IF tracking_record.all_time_high_cents IS NULL OR new_price_cents > tracking_record.all_time_high_cents THEN
        tracking_record.all_time_high_cents := new_price_cents;
        tracking_record.all_time_high_updated := NOW();
        needs_update := TRUE;
    END IF;

    -- Update record if any datapoint changed
    IF needs_update THEN
        UPDATE steam_price_tracking SET
            original_price_cents = tracking_record.original_price_cents,
            all_time_low_cents = tracking_record.all_time_low_cents,
            all_time_high_cents = tracking_record.all_time_high_cents,
            original_price_updated = tracking_record.original_price_updated,
            all_time_low_updated = tracking_record.all_time_low_updated,
            all_time_high_updated = tracking_record.all_time_high_updated,
            updated_at = NOW()
        WHERE id = tracking_record.id;
        
        RAISE NOTICE 'Steam tracking updated: game=%, currency=%, original=%, low=%, high=%, current=%', 
            NEW.game_id, NEW.currency, tracking_record.original_price_cents, 
            tracking_record.all_time_low_cents, tracking_record.all_time_high_cents, new_price_cents;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
CREATE TRIGGER trigger_update_steam_price_tracking
    AFTER INSERT OR UPDATE ON game_prices
    FOR EACH ROW
    EXECUTE FUNCTION update_steam_price_tracking();

-- Force update existing Steam prices to trigger the new logic
UPDATE game_prices SET last_updated = NOW() WHERE store_name = 'Steam';

SELECT 'Fixed original price logic - Steam prices will now track original_price field correctly' as status;