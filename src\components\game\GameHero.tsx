'use client';

import Image from 'next/image';
import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { GameData, GameStats } from '@/lib/services/gameService';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock, Users, Gamepad2, BarChart3, ExternalLink } from 'lucide-react';
import { useBackgroundBrightness } from '@/hooks/useBackgroundBrightness';
import { useSteamGridDBHeroAuto, useSteamGridDBHeroWithFetch } from '@/hooks/useSteamGridDBHero';
import GameViewLineChart from '@/components/game/GameViewLineChart';
import GamePerformanceSurveys from '@/components/game/GamePerformanceSurveys';
import GamePricesWidgetInteractive from '@/components/game/GamePricesWidgetInteractive';
import SteamPriceHistory<PERSON>hart from '@/components/game/SteamPriceHistoryChart';
import SteamGridDBCredit from '@/components/ui/SteamGridDBCredit';


interface GameHeroProps {
  game: GameData;
  stats: GameStats;
}

export default function GameHero({ game, stats }: GameHeroProps) {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [backgroundImageUrl, setBackgroundImageUrl] = useState<string | null>(null);
  const [activeSection, setActiveSection] = useState('info');
  const [hoveredBadge, setHoveredBadge] = useState<string | null>(null);
  const [showFullSummary, setShowFullSummary] = useState(false);
  const [showPriceHistory, setShowPriceHistory] = useState(false);
  const [showCredits, setShowCredits] = useState(false);

  const [showAllPlatforms, setShowAllPlatforms] = useState(false);
  const [showAllGenres, setShowAllGenres] = useState(false);

  // Height synchronization state
  const [priceWidgetHeight, setPriceWidgetHeight] = useState<number>(0);
  const priceWidgetRef = useRef<HTMLDivElement>(null);

  // Character limit for summary text (based on provided example)
  const SUMMARY_CHAR_LIMIT = 330;

  // Use the background brightness hook for dynamic text colors
  const isDarkBackground = useBackgroundBrightness();

  // SteamGridDB hero banner hook with automatic processing
  const { heroInfo, loading: heroLoading } = useSteamGridDBHeroAuto(game.id);

  // Height change handler
  const handlePriceWidgetHeightChange = (height: number) => {
    setPriceWidgetHeight(height);
  };

  useEffect(() => {
    // Priority 1: SteamGridDB hero banner (1920x620)
    if (heroInfo?.heroUrl && heroInfo.status === 'cached') {
      setBackgroundImageUrl(heroInfo.heroUrl);
      return;
    }

    // Priority 2: Cached Supabase cover
    if (game.supabase_cover_url) {
      setBackgroundImageUrl(game.supabase_cover_url);
      return;
    }

    // Priority 3: Original IGDB cover
    if (game.cover_url) {
      setBackgroundImageUrl(game.cover_url);
    }
  }, [heroInfo?.heroUrl, heroInfo?.status, game.supabase_cover_url, game.cover_url]);

  const formatReleaseDate = (dateStr: string | null) => {
    if (!dateStr) return 'TBA';
    try {
      return new Date(dateStr).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return dateStr;
    }
  };

  const formatPlayTime = (minutes: number | null) => {
    if (!minutes) return null;
    const hours = Math.round(minutes / 60 * 10) / 10;
    return `${hours}h`;
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return '#10b981'; // green
    if (score >= 80) return '#3b82f6'; // blue
    if (score >= 70) return '#f59e0b'; // orange
    if (score >= 60) return '#8b5cf6'; // purple
    return '#6b7280'; // gray
  };

  const getScoreTier = (score: number) => {
    if (score >= 90) return 'EXCELLENT';
    if (score >= 80) return 'GREAT';
    if (score >= 70) return 'GOOD';
    if (score >= 60) return 'AVERAGE';
    return 'BELOW AVERAGE';
  };

  return (
    <motion.div 
      className="relative min-h-[500px] overflow-hidden"
      layout
      transition={{ 
        layout: { 
          duration: 0.4, 
          ease: [0.4, 0, 0.2, 1] 
        } 
      }}
    >
      {/* Simplified Background */}
      {backgroundImageUrl && (
        <div className="absolute inset-0 z-0">
          <Image
            src={backgroundImageUrl}
            alt={`${game.name} background`}
            fill
            className="object-cover object-center scale-110 blur-sm"
            onLoad={() => setImageLoaded(true)}
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-slate-950/95 via-slate-900/80 to-slate-950/95" />
          <div className="absolute inset-0 bg-gradient-to-t from-slate-950/90 via-transparent to-slate-900/60" />


        </div>
      )}

      {/* Main Content Container */}
      <div className="relative z-10 container mx-auto px-4 py-8">
        <div className="flex flex-col lg:flex-row gap-8 lg:items-center items-start">
          
          {/* Left Column - Game Cover and Navigation */}
          <div className="flex-shrink-0 mx-auto lg:mx-0 flex flex-col justify-center">
            {/* Game Cover */}
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="mb-3"
            >
              <div className="relative w-48 lg:w-56">
                <div className="aspect-[3/4] relative rounded-lg overflow-hidden bg-slate-800/60 border border-transparent shadow-lg shadow-black/40">
                  {game.supabase_cover_url || game.cover_url ? (
                    <Image
                      src={game.supabase_cover_url || game.cover_url || ''}
                      alt={`${game.name} cover`}
                      fill
                      className="object-cover"
                      priority
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <Gamepad2 className="w-10 h-10 text-slate-400" />
                    </div>
                  )}
                </div>
              </div>
            </motion.div>

            {/* Tab Navigation - Below cover */}
            <motion.div 
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="w-48 lg:w-56 space-y-2"
            >
              <div className="grid grid-cols-2 gap-2">
                {[
                  { id: 'info', label: 'Data' },
                  { id: 'details', label: 'Details' }
                ].map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveSection(tab.id)}
                    className={`px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                      activeSection === tab.id
                        ? 'bg-violet-600/60 text-white border border-violet-400/40 shadow-lg shadow-violet-600/20'
                        : 'bg-slate-800/40 text-slate-300 border border-slate-600/30 hover:bg-slate-800/60 hover:text-white'
                    }`}
                  >
                    {tab.label}
                  </button>
                ))}
              </div>
              
              {/* Price History Button */}
              {activeSection === 'info' && (
                <motion.button
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.1 }}
                  onClick={() => setShowPriceHistory(!showPriceHistory)}
                  className={`w-full px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                    showPriceHistory
                      ? 'bg-green-600/60 text-white border border-green-400/40 shadow-lg shadow-green-600/20'
                      : 'bg-slate-800/40 text-slate-300 border border-slate-600/30 hover:bg-slate-800/60 hover:text-white'
                  }`}
                >
                  {showPriceHistory ? 'Show Views' : 'Price History'}
                </motion.button>
              )}
            </motion.div>
          </div>

          {/* Right Column - Game Information */}
          <div className="flex-1 space-y-6 text-center lg:text-left">
            
            {/* Title Only */}
            <motion.div
              className="flex items-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              {/* Game Title with Code Style */}
              <div className="flex-1 min-w-0">
                <h1 className="text-2xl lg:text-4xl font-mono font-bold text-white truncate">
                  <span className="text-violet-400">//</span> {game.name}
                </h1>

                {/* SteamGridDB Credit under game name */}
                {heroInfo?.credits && heroInfo.status === 'cached' && (
                  <motion.div
                    className="mt-0.5 flex items-center gap-1.5 text-[10px] text-slate-500"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.3, delay: 0.3 }}
                  >
                    <span>Banner from</span>
                    <a
                      href={`https://www.steamgriddb.com/game/${heroInfo.credits.gameId}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-violet-400 hover:text-violet-300 transition-colors flex items-center gap-0.5"
                    >
                      SteamGridDB
                      <ExternalLink className="w-2.5 h-2.5" />
                    </a>
                    {heroInfo.credits.authorName && (
                      <>
                        <span>•</span>
                        <span>by</span>
                        {heroInfo.credits.authorSteam64 ? (
                          <a
                            href={`https://steamcommunity.com/profiles/${heroInfo.credits.authorSteam64}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-slate-400 hover:text-slate-300 transition-colors flex items-center gap-0.5"
                          >
                            {heroInfo.credits.authorName}
                            <ExternalLink className="w-2.5 h-2.5" />
                          </a>
                        ) : (
                          <span className="text-slate-400">
                            {heroInfo.credits.authorName}
                          </span>
                        )}
                      </>
                    )}
                  </motion.div>
                )}
              </div>
            </motion.div>

            {/* Content Sections */}
            <AnimatePresence mode="wait">
              <motion.div
                key={activeSection}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.2 }}
                layout
                className="min-h-[180px]"
              >
                {activeSection === 'info' && (
                  <motion.div 
                    className="space-y-4"
                    layout
                    transition={{ 
                      layout: { 
                        duration: 0.3, 
                        ease: "easeInOut" 
                      } 
                    }}
                  >
                    {/* Analytics Chart and Price Widget Container */}
                    <div className="flex gap-4 mb-4">
                      {/* Analytics Chart or Price History - 75% width */}
                      <div
                        className="w-3/4"
                        style={{
                          height: priceWidgetHeight > 0 ? `${Math.max(priceWidgetHeight, 200)}px` : 'auto',
                          minHeight: '200px'
                        }}
                      >
                        {(() => {
                          const chartHeight = priceWidgetHeight > 0 ? Math.max(priceWidgetHeight, 200) : undefined;

                          return showPriceHistory ? (
                            <SteamPriceHistoryChart
                              gameId={game.id}
                              daysBack={90}
                              height={chartHeight}
                            />
                          ) : (
                            <GameViewLineChart
                              gameId={game.id}
                              height={chartHeight}
                            />
                          );
                        })()}
                      </div>

                      {/* Price Widget - 25% width */}
                      <div className="w-1/4">
                        <GamePricesWidgetInteractive
                          ref={priceWidgetRef}
                          gameId={game.id}
                          onHeightChange={handlePriceWidgetHeightChange}
                        />
                      </div>
                    </div>

                    {/* Combined Stats Container */}
                    <motion.div 
                      className="bg-slate-800/40 border border-slate-600/30 rounded-lg px-4 py-3 hover:bg-slate-800/60 transition-all duration-200"
                      whileHover={{ scale: 1.01 }}
                      transition={{ duration: 0.2 }}
                    >
                      <div className={`grid grid-cols-1 gap-3 ${stats.total_performance_surveys > 0 ? 'lg:grid-cols-3' : 'lg:grid-cols-2'}`}>
                        {/* Average Score */}
                        {stats.average_rating && (
                          <div className="space-y-2">
                            <div className="text-xs text-slate-500 font-mono uppercase tracking-wider">
                              average score
                            </div>
                            <div className="flex items-center gap-2">
                              <span className="text-sm font-bold text-white font-mono">
                                {Math.round(stats.average_rating)}
                              </span>
                              <div className="flex-1 h-1 bg-slate-700/50 rounded-full overflow-hidden">
                                <motion.div
                                  className="h-full rounded-full transition-all duration-1000 ease-out"
                                  style={{ 
                                    backgroundColor: getScoreColor(stats.average_rating),
                                    width: `${stats.average_rating}%`
                                  }}
                                  initial={{ width: 0 }}
                                  animate={{ width: `${stats.average_rating}%` }}
                                  transition={{ duration: 1, delay: 0.3 }}
                                />
                              </div>
                            </div>
                          </div>
                        )}

                        {/* Hardware Surveys - Only show if there are surveys */}
                        {stats.total_performance_surveys > 0 && (
                          <div className="space-y-2">
                            <div className="text-xs text-slate-500 font-mono uppercase tracking-wider">
                              hardware surveys
                            </div>
                            <div className="flex items-center gap-2">
                              <span className="text-sm font-bold text-white font-mono">
                                {stats.total_performance_surveys.toLocaleString()}
                              </span>
                              <div className="flex-1 h-1 bg-slate-700/50 rounded-full overflow-hidden">
                                <motion.div
                                  className="h-full rounded-full bg-blue-500 transition-all duration-1000 ease-out"
                                  style={{ width: '75%' }}
                                  initial={{ width: 0 }}
                                  animate={{ width: '75%' }}
                                  transition={{ duration: 1, delay: 0.5 }}
                                />
                              </div>
                            </div>
                          </div>
                        )}

                        {/* Total Reviews */}
                        <div className="space-y-2">
                          <div className="text-xs text-slate-500 font-mono uppercase tracking-wider">
                            total reviews
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-bold text-white font-mono">
                              {stats.total_reviews.toLocaleString()}
                            </span>
                            <div className="flex-1 h-1 bg-slate-700/50 rounded-full overflow-hidden">
                              <motion.div
                                className="h-full rounded-full bg-green-500 transition-all duration-1000 ease-out"
                                style={{ width: '60%' }}
                                initial={{ width: 0 }}
                                animate={{ width: '60%' }}
                                transition={{ duration: 1, delay: 0.7 }}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  </motion.div>
                )}

                {activeSection === 'details' && (
                  <motion.div 
                    className="space-y-4"
                    layout
                    transition={{ 
                      layout: { 
                        duration: 0.3, 
                        ease: "easeInOut" 
                      } 
                    }}
                  >
                    {/* Game Summary - Primary Card */}
                    {game.summary && (
                      <motion.div 
                        className="bg-slate-800/40 border border-slate-600/30 rounded-lg p-4 hover:bg-slate-800/60 transition-all duration-200"
                        whileHover={{ scale: 1.01 }}
                        transition={{ duration: 0.2 }}
                      >
                        <div className="flex items-center gap-2 mb-3">
                          <div className="w-1 h-4 bg-violet-500 rounded-full"></div>
                          <h3 className="text-sm font-mono text-slate-400 tracking-wider">
                            about {game.name}
                          </h3>
                        </div>
                        <motion.div 
                          className="relative"
                          layout
                          transition={{ 
                            layout: { 
                              duration: 0.4, 
                              ease: [0.4, 0, 0.2, 1] 
                            } 
                          }}
                        >
                          <motion.p 
                            className="text-sm text-slate-300 leading-relaxed"
                            layout
                            transition={{ 
                              layout: { 
                                duration: 0.4, 
                                ease: [0.4, 0, 0.2, 1] 
                              } 
                            }}
                          >
                            {showFullSummary || game.summary.length <= SUMMARY_CHAR_LIMIT
                              ? game.summary
                              : `${game.summary.substring(0, SUMMARY_CHAR_LIMIT)}...`
                            }
                          </motion.p>
                          {game.summary.length > SUMMARY_CHAR_LIMIT && (
                            <button
                              onClick={() => setShowFullSummary(!showFullSummary)}
                              className="mt-2 text-violet-400 hover:text-violet-300 text-sm font-medium transition-colors duration-200 flex items-center gap-1"
                            >
                              {showFullSummary ? 'Read Less' : 'Read More'}
                              <motion.div
                                animate={{ 
                                  rotate: showFullSummary ? 180 : 0 
                                }}
                                transition={{ duration: 0.2 }}
                                className="w-3 h-3"
                              >
                                <svg viewBox="0 0 12 12" fill="none" className="w-full h-full">
                                  <path 
                                    d="M3 4.5L6 7.5L9 4.5" 
                                    stroke="currentColor" 
                                    strokeWidth="1.5" 
                                    strokeLinecap="round" 
                                    strokeLinejoin="round"
                                  />
                                </svg>
                              </motion.div>
                            </button>
                          )}
                        </motion.div>
                      </motion.div>
                    )}

                    {/* Playtime Info Card */}
                    {(game.time_to_beat_normally || game.time_to_beat_completely) && (
                      <motion.div 
                        className="bg-slate-800/40 border border-slate-600/30 rounded-lg p-3 hover:bg-slate-800/60 transition-all duration-200"
                        whileHover={{ scale: 1.02 }}
                        transition={{ duration: 0.2 }}
                      >
                        <div className="flex items-center gap-2 mb-2">
                          <Clock className="w-4 h-4 text-violet-400" />
                          <span className="text-xs text-slate-400 uppercase tracking-wider font-mono">Play Time</span>
                        </div>
                        <div className="space-y-1">
                          {game.time_to_beat_normally && (
                            <div className="text-sm text-slate-300">
                              <span className="text-slate-400">Main Story:</span> {formatPlayTime(game.time_to_beat_normally)}
                            </div>
                          )}
                          {game.time_to_beat_completely && (
                            <div className="text-sm text-slate-300">
                              <span className="text-slate-400">Complete:</span> {formatPlayTime(game.time_to_beat_completely)}
                            </div>
                          )}
                        </div>
                      </motion.div>
                    )}

                    {/* Platforms & Genres */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
                      {/* Platforms Card */}
                      {game.platforms && game.platforms.length > 0 && (
                        <motion.div 
                          className="bg-slate-800/40 border border-slate-600/30 rounded-lg p-3 hover:bg-slate-800/60 transition-all duration-200"
                          whileHover={{ scale: 1.02 }}
                          transition={{ duration: 0.2 }}
                        >
                          <div className="flex items-center gap-2 mb-3">
                            <div className="w-4 h-4 flex items-center justify-center">
                              <div className="w-2 h-2 bg-violet-400 rounded-full"></div>
                            </div>
                            <span className="text-xs text-slate-400 uppercase tracking-wider font-mono">
                              platform{game.platforms.length > 1 ? 's' : ''}
                            </span>
                          </div>
                          <div className="flex flex-wrap gap-1.5">
                            {(showAllPlatforms ? game.platforms : game.platforms.slice(0, 3)).map((platform, index) => (
                              <span 
                                key={index}
                                className="px-2 py-1 text-xs bg-slate-800/60 text-slate-300 border border-slate-600/40 rounded-md font-mono hover:bg-slate-800/80 hover:border-slate-500/60 transition-all duration-200"
                              >
                                {platform}
                              </span>
                            ))}
                            {game.platforms.length > 3 && (
                              <button
                                className="px-2 py-1 text-xs bg-slate-800/40 text-violet-400 border border-violet-500/30 rounded-md font-mono cursor-pointer hover:bg-violet-500/10 hover:border-violet-500/50 transition-all duration-200"
                                onClick={() => setShowAllPlatforms(!showAllPlatforms)}
                              >
                                {showAllPlatforms 
                                  ? 'show less' 
                                  : `+${game.platforms.length - 3} more`
                                }
                              </button>
                            )}
                          </div>
                        </motion.div>
                      )}

                      {/* Genres Card */}
                      {game.genres && game.genres.length > 0 && (
                        <motion.div 
                          className="bg-slate-800/40 border border-slate-600/30 rounded-lg p-3 hover:bg-slate-800/60 transition-all duration-200"
                          whileHover={{ scale: 1.02 }}
                          transition={{ duration: 0.2 }}
                        >
                          <div className="flex items-center gap-2 mb-3">
                            <div className="w-4 h-4 flex items-center justify-center">
                              <div className="w-2 h-2 bg-violet-400 rounded-full"></div>
                            </div>
                            <span className="text-xs text-slate-400 uppercase tracking-wider font-mono">
                              genre{game.genres.length > 1 ? 's' : ''}
                            </span>
                          </div>
                          <div className="flex flex-wrap gap-1.5">
                            {(showAllGenres ? game.genres : game.genres.slice(0, 3)).map((genre, index) => (
                              <span 
                                key={index}
                                className="px-2 py-1 text-xs bg-violet-500/10 text-violet-300 border border-violet-500/30 rounded-md font-mono hover:bg-violet-500/20 hover:border-violet-500/50 transition-all duration-200"
                              >
                                {genre}
                              </span>
                            ))}
                            {game.genres.length > 3 && (
                              <button
                                className="px-2 py-1 text-xs bg-slate-800/40 text-violet-400 border border-violet-500/30 rounded-md font-mono cursor-pointer hover:bg-violet-500/10 hover:border-violet-500/50 transition-all duration-200"
                                onClick={() => setShowAllGenres(!showAllGenres)}
                              >
                                {showAllGenres 
                                  ? 'show less' 
                                  : `+${game.genres.length - 3} more`
                                }
                              </button>
                            )}
                          </div>
                        </motion.div>
                      )}
                    </div>

                    {/* Development Team & Game Info - Combined Card */}
                    <motion.div
                      className="bg-slate-800/40 border border-slate-600/30 rounded-lg p-4 hover:bg-slate-800/60 transition-all duration-200"
                      whileHover={{ scale: 1.01 }}
                      transition={{ duration: 0.2 }}
                    >


                      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                        {/* Release Date Section */}
                        <div>
                          <div className="flex items-center gap-2 mb-2">
                            <div className="w-3 h-3 flex items-center justify-center">
                              <div className="w-1.5 h-1.5 bg-violet-400 rounded-full"></div>
                            </div>
                            <span className="text-xs text-slate-400 uppercase tracking-wider font-mono">
                              release date
                            </span>
                          </div>
                          <div className="text-xs text-slate-300 font-mono truncate">
                            {formatReleaseDate(game.release_date)}
                          </div>
                        </div>

                        {/* Developers Section */}
                        {game.developers && game.developers.length > 0 && (
                          <div>
                            <div className="flex items-center gap-2 mb-2">
                              <div className="w-3 h-3 flex items-center justify-center">
                                <div className="w-1.5 h-1.5 bg-violet-400 rounded-full"></div>
                              </div>
                              <span className="text-xs text-slate-400 uppercase tracking-wider font-mono">
                                developer{game.developers.length > 1 ? 's' : ''}
                              </span>
                            </div>
                            <div className="space-y-1">
                              <div className="text-xs text-slate-300 font-mono truncate">
                                {game.developers[0]}
                              </div>
                            </div>
                          </div>
                        )}

                        {/* Publishers Section */}
                        {game.publishers && game.publishers.length > 0 && (
                          <div>
                            <div className="flex items-center gap-2 mb-2">
                              <div className="w-3 h-3 flex items-center justify-center">
                                <div className="w-1.5 h-1.5 bg-violet-400 rounded-full"></div>
                              </div>
                              <span className="text-xs text-slate-400 uppercase tracking-wider font-mono">
                                publisher{game.publishers.length > 1 ? 's' : ''}
                              </span>
                            </div>
                            <div className="space-y-1">
                              <div className="text-xs text-slate-300 font-mono truncate">
                                {game.publishers[0]}
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </motion.div>

                    {/* SteamGridDB Credit Card - Only show if using SteamGridDB hero */}
                    {heroInfo?.credits && heroInfo.status === 'cached' && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: 0.2 }}
                      >
                        <SteamGridDBCredit
                          credits={heroInfo.credits}
                          variant="default"
                        />
                      </motion.div>
                    )}
                  </motion.div>
                )}
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </div>
    </motion.div>
  );
}