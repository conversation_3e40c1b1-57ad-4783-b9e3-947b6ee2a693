import { NextRequest, NextResponse } from 'next/server';
import { SteamGridDBHeroService } from '@/lib/services/steamGridDBHeroService';
import { getGameById } from '@/lib/services/gameService';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { error: 'Game ID is required' },
        { status: 400 }
      );
    }

    // Get game information
    const game = await getGameById(id);
    
    if (!game) {
      return NextResponse.json(
        { error: 'Game not found' },
        { status: 404 }
      );
    }

    // Check if hero processing is needed
    const needsProcessing = await SteamGridDBHeroService.needsHeroProcessing(id);
    
    if (!needsProcessing) {
      // Return cached hero info
      const cachedInfo = await SteamGridDBHeroService.getCachedHeroInfo(id);
      return NextResponse.json({
        success: true,
        cached: true,
        message: 'Hero banner already cached',
        data: cachedInfo
      });
    }

    // Trigger hero banner processing
    SteamGridDBHeroService.triggerHeroProcessing(id, game.name);

    return NextResponse.json({
      success: true,
      cached: false,
      message: 'Hero banner processing started',
      data: {
        status: 'processing'
      }
    });

  } catch (error) {
    console.error('Game hero fetch error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch hero banner for game',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { error: 'Game ID is required' },
        { status: 400 }
      );
    }

    // Get cached hero information
    const cachedInfo = await SteamGridDBHeroService.getCachedHeroInfo(id);

    return NextResponse.json({
      success: true,
      data: cachedInfo
    });

  } catch (error) {
    console.error('Game hero info error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to get hero info for game',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
