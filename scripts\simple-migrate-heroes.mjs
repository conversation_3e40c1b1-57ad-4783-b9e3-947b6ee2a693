#!/usr/bin/env node

/**
 * Simple SteamGridDB Hero Banner Migration Script
 * Uses existing API functions and services
 */

import { createClient } from '@supabase/supabase-js';
import { searchSteamGridDBGames, getSteamGridDBHeroes } from '../src/lib/steamgriddb-api.js';
import { ImageOptimizer } from '../src/lib/performance/imageOptimization.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

const HERO_BUCKET = 'steamgriddb-heroes';
const TARGET_WIDTH = 1920;
const TARGET_HEIGHT = 620;

// Select best hero banner
function selectBestHero(heroes) {
  if (!heroes.length) return null;

  const targetAspectRatio = TARGET_WIDTH / TARGET_HEIGHT;

  return heroes.sort((a, b) => {
    // Priority 1: Exact dimensions
    const aExactDimensions = a.width === TARGET_WIDTH && a.height === TARGET_HEIGHT;
    const bExactDimensions = b.width === TARGET_WIDTH && b.height === TARGET_HEIGHT;
    
    if (aExactDimensions && !bExactDimensions) return -1;
    if (!aExactDimensions && bExactDimensions) return 1;

    // Priority 2: Score (higher is better)
    if (a.score !== b.score) return b.score - a.score;

    // Priority 3: Aspect ratio (closer to target is better)
    const aAspectRatio = a.width / a.height;
    const bAspectRatio = b.width / b.height;
    const aAspectDiff = Math.abs(aAspectRatio - targetAspectRatio);
    const bAspectDiff = Math.abs(bAspectRatio - targetAspectRatio);
    
    return aAspectDiff - bAspectDiff;
  })[0];
}

// Process single game
async function processGame(game) {
  const startTime = Date.now();
  
  try {
    console.log(`🎮 Processing: ${game.name}`);

    // Update status to processing
    await supabase.rpc('update_steamgriddb_hero_cache_status', {
      game_id: game.id,
      new_status: 'processing'
    });

    // Search for the game on SteamGridDB
    const games = await searchSteamGridDBGames(game.name);
    
    if (!games || games.length === 0) {
      throw new Error(`No games found on SteamGridDB for: ${game.name}`);
    }

    const steamGridDBGame = games[0];
    console.log(`   Found SteamGridDB game: ${steamGridDBGame.name} (ID: ${steamGridDBGame.id})`);
    
    // Fetch hero banners
    const heroes = await getSteamGridDBHeroes(steamGridDBGame.id, { limit: 5 });

    if (!heroes || heroes.length === 0) {
      throw new Error(`No hero banners found for: ${game.name}`);
    }

    // Select best hero
    const bestHero = selectBestHero(heroes);
    
    if (!bestHero) {
      throw new Error(`No suitable hero banner found for: ${game.name}`);
    }

    console.log(`   Selected hero: ${bestHero.width}x${bestHero.height} by ${bestHero.author?.name || 'Unknown'} (Score: ${bestHero.score})`);

    // Download the image
    const response = await fetch(bestHero.url);
    
    if (!response.ok) {
      throw new Error(`Failed to download hero: ${response.status}`);
    }

    const imageBuffer = await response.arrayBuffer();
    const buffer = Buffer.from(imageBuffer);

    // Optimize the image using your existing ImageOptimizer
    const optimized = await ImageOptimizer.optimizeImage(buffer, {
      width: TARGET_WIDTH,
      height: TARGET_HEIGHT,
      quality: 85,
      format: 'webp',
      stripMetadata: true
    });

    // Generate filename
    const filename = `${game.id}-${bestHero.id}-${Date.now()}.webp`;
    
    // Upload to Supabase storage
    const { data, error } = await supabase.storage
      .from(HERO_BUCKET)
      .upload(filename, optimized.buffer, {
        contentType: 'image/webp',
        cacheControl: '31536000'
      });

    if (error) {
      throw new Error(`Failed to upload to storage: ${error.message}`);
    }

    // Get public URL
    const { data: urlData } = supabase.storage
      .from(HERO_BUCKET)
      .getPublicUrl(filename);

    // Update game record
    await supabase.rpc('update_steamgriddb_hero_cache_status', {
      game_id: game.id,
      new_status: 'cached',
      cached_url: urlData.publicUrl,
      author_name: bestHero.author?.name || null,
      author_steam64: bestHero.author?.steam64 || null,
      author_avatar: bestHero.author?.avatar || null,
      hero_id: bestHero.id,
      sgdb_game_id: steamGridDBGame.id
    });

    // Log success
    await supabase.rpc('log_steamgriddb_hero_audit', {
      p_game_id: game.id,
      p_action: 'fetch_completed',
      p_steamgriddb_game_id: steamGridDBGame.id,
      p_steamgriddb_hero_id: bestHero.id,
      p_original_url: bestHero.url,
      p_supabase_url: urlData.publicUrl,
      p_author_name: bestHero.author?.name || null,
      p_author_steam64: bestHero.author?.steam64 || null,
      p_processing_time_ms: Date.now() - startTime,
      p_file_size_bytes: optimized.size,
      p_dimensions: `${bestHero.width}x${bestHero.height}`
    });

    console.log(`   ✅ Success! Hero cached: ${urlData.publicUrl}`);
    console.log(`   📊 Original: ${buffer.length} bytes → Optimized: ${optimized.size} bytes (${Math.round((1 - optimized.size / buffer.length) * 100)}% reduction)`);
    
    return { success: true, heroUrl: urlData.publicUrl };

  } catch (error) {
    console.log(`   ❌ Failed: ${error.message}`);
    
    // Update status to failed
    await supabase.rpc('update_steamgriddb_hero_cache_status', {
      game_id: game.id,
      new_status: 'failed'
    });

    // Log failure
    await supabase.rpc('log_steamgriddb_hero_audit', {
      p_game_id: game.id,
      p_action: 'fetch_failed',
      p_error_message: error.message,
      p_processing_time_ms: Date.now() - startTime
    });

    return { success: false, error: error.message };
  }
}

// Get games needing processing
async function getGamesNeedingProcessing(limit = 10) {
  const { data, error } = await supabase
    .from('games')
    .select('id, name, slug, steamgriddb_hero_cache_status')
    .or('steamgriddb_hero_cache_status.is.null,steamgriddb_hero_cache_status.eq.pending,steamgriddb_hero_cache_status.eq.failed')
    .order('created_at', { ascending: true })
    .limit(limit);

  if (error) {
    throw error;
  }

  return data || [];
}

// Get migration stats
async function getStats() {
  try {
    console.log('📊 Getting migration statistics...\n');

    // Get total games count
    const { count: totalGames } = await supabase
      .from('games')
      .select('*', { count: 'exact', head: true });

    // Get games by hero cache status
    const { data: statusCounts } = await supabase
      .from('games')
      .select('steamgriddb_hero_cache_status')
      .not('steamgriddb_hero_cache_status', 'is', null);

    const stats = {
      totalGames: totalGames || 0,
      processed: 0,
      successful: 0,
      failed: 0,
      skipped: 0,
      inProgress: 0
    };

    if (statusCounts) {
      statusCounts.forEach(game => {
        const status = game.steamgriddb_hero_cache_status;
        stats.processed++;
        
        switch (status) {
          case 'cached':
            stats.successful++;
            break;
          case 'failed':
            stats.failed++;
            break;
          case 'processing':
            stats.inProgress++;
            break;
          default:
            stats.skipped++;
        }
      });
    }

    console.log('📈 Migration Statistics:');
    console.log(`   Total Games: ${stats.totalGames.toLocaleString()}`);
    console.log(`   Processed: ${stats.processed.toLocaleString()}`);
    console.log(`   Successful: ${stats.successful.toLocaleString()}`);
    console.log(`   Failed: ${stats.failed.toLocaleString()}`);
    console.log(`   In Progress: ${stats.inProgress.toLocaleString()}`);
    console.log(`   Remaining: ${(stats.totalGames - stats.processed).toLocaleString()}`);
    
    const percentage = stats.totalGames > 0 ? ((stats.processed / stats.totalGames) * 100).toFixed(1) : 0;
    console.log(`   Progress: ${percentage}%\n`);

    return stats;
  } catch (error) {
    console.error('❌ Failed to get stats:', error.message);
    throw error;
  }
}

// Main migration function
async function runMigration() {
  console.log('🚀 Starting SteamGridDB hero banner migration...\n');

  if (!process.env.STEAMGRIDDB_API_KEY) {
    console.error('❌ STEAMGRIDDB_API_KEY not found in environment variables');
    process.exit(1);
  }

  try {
    let totalProcessed = 0;
    let totalSuccessful = 0;
    let totalFailed = 0;

    while (true) {
      // Get next batch of games
      const games = await getGamesNeedingProcessing(3); // Small batch to start
      
      if (games.length === 0) {
        console.log('🎉 No more games to process!');
        break;
      }

      console.log(`📦 Processing batch of ${games.length} games...\n`);

      // Process games one by one to avoid overwhelming the API
      for (const game of games) {
        const result = await processGame(game);
        
        totalProcessed++;
        if (result.success) {
          totalSuccessful++;
        } else {
          totalFailed++;
        }

        // Small delay between games to be respectful to SteamGridDB API
        await new Promise(resolve => setTimeout(resolve, 2000));
      }

      console.log(`\n📊 Batch complete: ${totalSuccessful}/${totalProcessed} successful so far\n`);

      // Delay between batches
      await new Promise(resolve => setTimeout(resolve, 3000));
    }

    console.log(`\n🎉 Migration complete!`);
    console.log(`📊 Final results: ${totalSuccessful} successful, ${totalFailed} failed out of ${totalProcessed} total\n`);

    // Show final stats
    await getStats();

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Parse command line arguments
const args = process.argv.slice(2);

if (args.includes('--stats')) {
  getStats().catch(console.error);
} else {
  runMigration().catch(console.error);
}
