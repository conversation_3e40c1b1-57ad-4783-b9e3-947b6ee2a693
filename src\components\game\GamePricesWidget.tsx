'use client';


import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ShoppingCart, ExternalLink, Percent, Clock, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  POPULAR_STEAM_REGIONS, 
  DEFAULT_REGION, 
  getRegionByCode, 
  type SteamRegion 
} from '@/lib/constants/steamRegions';
import { useCurrency } from '@/contexts/currency-context';

interface GamePrice {
  id: string;
  store_name: string;
  price: string;
  original_price?: string;
  discount_percentage?: number;
  store_url: string;
  affiliate_url?: string; // Added for affiliate tracking
  currency: string;
  availability: 'available' | 'out_of_stock' | 'pre_order';
  last_updated: string;
  region_code: string;
  display_order?: number;
}

interface RegionInfo {
  code: string;
  name: string;
  currency: string;
  currencySymbol: string;
  flag: string;
}

interface GamePricesResponse {
  prices: GamePrice[];
  region: RegionInfo;
  needsUpdate: boolean;
  lastUpdate: string | null;
  count: number;
  freshUpdate?: boolean; // Added for force update detection
  cacheInfo?: {
    hoursSinceUpdate: number;
    cacheExpired: boolean;
    autoRefreshTriggered: boolean;
  };
}

interface GamePricesWidgetProps {
  gameId: string;
  gameName?: string;
  className?: string;
}

const storeColors: Record<string, string> = {
  'Steam': 'text-blue-400',
  'Epic Games': 'text-yellow-400',
  'GOG': 'text-purple-400',
  'Nuuvem': 'text-green-400',
  'PlayStation': 'text-blue-500',
  'Xbox': 'text-green-500',
  'Nintendo': 'text-red-400',
  'Instant Gaming': 'text-orange-400',
  'Kinguin': 'text-orange-500', // Added Kinguin brand color
  'Fanatical': 'text-red-500', // Added Fanatical brand color
  'Gamivo': 'text-indigo-400' // Added Gamivo brand color
};

const storeGradients: Record<string, string> = {
  'Steam': 'from-blue-600 to-blue-500',
  'Epic Games': 'from-yellow-600 to-yellow-500',
  'GOG': 'from-purple-600 to-purple-500',
  'Nuuvem': 'from-green-600 to-green-500',
  'PlayStation': 'from-blue-700 to-blue-600',
  'Xbox': 'from-green-700 to-green-600',
  'Nintendo': 'from-red-600 to-red-500',
  'Instant Gaming': 'from-orange-600 to-orange-500',
  'Kinguin': 'from-orange-700 to-orange-600', // Added Kinguin gradient
  'Fanatical': 'from-red-700 to-red-600', // Added Fanatical gradient
  'Gamivo': 'from-indigo-700 to-indigo-600' // Added Gamivo gradient
};

const STORE_COLORS = {
  'Steam': 'bg-blue-500',
  'Nuuvem': 'bg-green-500',
  'Epic Games': 'bg-gray-800',
  'Instant Gaming': 'bg-purple-500',
  'GOG': 'bg-purple-600',
  'Origin': 'bg-orange-500',
  'Kinguin': 'bg-orange-600', // Added Kinguin background color
  'Fanatical': 'bg-red-600', // Added Fanatical background color
  'Gamivo': 'bg-indigo-600' // Added Gamivo background color
};

const AVAILABILITY_LABELS = {
  'available': { text: 'Disponível', color: 'bg-green-100 text-green-800' },
  'out_of_stock': { text: 'Indisponível', color: 'bg-red-100 text-red-800' },
  'pre_order': { text: 'Pré-venda', color: 'bg-blue-100 text-blue-800' }
};

export default function GamePricesWidget({ 
  gameId, 
  gameName = '',
  className = ''
}: GamePricesWidgetProps) {
  const { selectedCurrency, currencyInfo, getKinguinCurrencyParam, getCurrencyByCode } = useCurrency();
  const [prices, setPrices] = useState<GamePrice[]>([]);
  const [selectedRegion, setSelectedRegion] = useState<string>(DEFAULT_REGION);
  const [regionInfo, setRegionInfo] = useState<RegionInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<string | null>(null);
  const [needsUpdate, setNeedsUpdate] = useState(false);
  const [cacheInfo, setCacheInfo] = useState<GamePricesResponse['cacheInfo']>();
  const [autoRefreshTriggered, setAutoRefreshTriggered] = useState(false);

  // Map currency to region codes - this could be more sophisticated
  const getCurrencyRegion = (currencyCode: string): string => {
    const currencyToRegion: Record<string, string> = {
      'USD': 'us',
      'EUR': 'de', // Use 'de' for EUR region (matches POPULAR_STEAM_REGIONS)
      'GBP': 'gb', // Use 'gb' not 'uk' (matches POPULAR_STEAM_REGIONS)
      'JPY': 'jp',
      'KRW': 'kr',
      'CNY': 'cn',
      'RUB': 'ru',
      'BRL': 'br',
      'MXN': 'mx',
      'CAD': 'ca',
      'AUD': 'au',
      'INR': 'in',
    };
    return currencyToRegion[currencyCode] || 'us';
  };

  // Update selected region when currency changes
  useEffect(() => {
    if (selectedCurrency) {
      const newRegion = getCurrencyRegion(selectedCurrency);
      if (newRegion !== selectedRegion) {
        setSelectedRegion(newRegion);
      }
    }
  }, [selectedCurrency]);

  // Additional safety check
  if (!gameId) {
    console.error('❌ GamePricesWidget: gameId is required but not provided');
    return (
      <Card className={`p-6 bg-slate-800/60 border-slate-600/50 ${className}`}>
        <div className="flex items-center gap-3">
          <ShoppingCart className="h-5 w-5 text-red-400" />
          <div>
            <div className="text-lg font-semibold text-white">Onde Comprar</div>
            <div className="text-sm text-red-400">❌ Erro: ID do jogo não fornecido</div>
          </div>
        </div>
      </Card>
    );
  }

  const fetchPrices = async (force = false, enableAutoRefresh = false, regionCode?: string) => {
    try {
      setError(null);
      if (force) setRefreshing(true);
      
      const region = regionCode || selectedRegion;
      
      // Build endpoint with region and autoRefresh parameters
      const baseEndpoint = `/api/games/${gameId}/prices`;
      const params = new URLSearchParams();
      params.set('region', region);
      if (enableAutoRefresh) params.set('autoRefresh', 'true');
      
      const endpoint = `${baseEndpoint}?${params.toString()}`;
      const method = force ? 'POST' : 'GET';
      const body = force ? JSON.stringify({ region, multiRegion: false }) : undefined;
      
      if (force) {
        console.log(`🔄 Force update will clear cache and fetch fresh data for all stores including Kinguin`);
        console.log(`🔍 API call details: gameId="${gameId}", region="${region}", endpoint="${endpoint}"`);
      }
      
      const response = await fetch(endpoint, { 
        method,
        headers: force ? { 'Content-Type': 'application/json' } : {},
        body
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`❌ Price fetch failed (${response.status}):`, errorText);
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data: GamePricesResponse = await response.json();
      console.log(`💰 Received ${data.count} prices:`, data.prices?.map(p => `${p.store_name}: ${p.price}`));
      
      // Handle fresh update from force refresh
      if (force && data.freshUpdate) {
        console.log(`🔥 Fresh data received from force update with timestamp: ${data.lastUpdate}`);
        setLastUpdate(data.lastUpdate || new Date().toISOString());
        setNeedsUpdate(false);
        setCacheInfo({
          hoursSinceUpdate: 0,
          cacheExpired: false,
          autoRefreshTriggered: false
        });
      } else {
        setLastUpdate(data.lastUpdate);
        setNeedsUpdate(data.needsUpdate || false);
        setCacheInfo(data.cacheInfo);
      }
      
      setPrices(data.prices || []);
      setRegionInfo(data.region);
      
      // Track if auto-refresh was triggered
      if (data.cacheInfo?.autoRefreshTriggered) {
        setAutoRefreshTriggered(true);
        console.log(`⚡ Auto-refresh triggered (cache was ${data.cacheInfo.hoursSinceUpdate}h old)`);
        
        // Clear the auto-refresh indicator after a few seconds
        setTimeout(() => setAutoRefreshTriggered(false), 5000);
      }
      
      if (force && data.count > 0) {
        console.log(`✅ Force update completed: ${data.count} stores found`);
        data.prices?.forEach(price => {
          console.log(`   • ${price.store_name}: ${price.price} (${price.currency})`);
        });
      } else if (force && data.count === 0) {
        console.warn(`⚠️ Force update completed but no prices found in region ${region}`);
      }
      
    } catch (err) {
      console.error('❌ Error fetching prices:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleForceUpdate = async () => {
    console.log(`🔄 Force update triggered in region ${selectedRegion}`);
    setRefreshing(true);
    setError(null);
    
    try {
      await fetchPrices(true);
    } catch (error) {
      console.error(`❌ Force update failed:`, error);
      setError('Failed to force update prices');
    } finally {
      setRefreshing(false);
    }
  };

  useEffect(() => {
    if (gameId) {
      // On initial load, enable auto-refresh to trigger background updates if needed
      fetchPrices(false, true);
    }
  }, [gameId, selectedRegion]);

  const formatLastUpdate = (updateTime: string | null) => {
    if (!updateTime) return 'Nunca';
    
    const date = new Date(updateTime);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    
    if (diffHours > 0) {
      return `${diffHours}h atrás`;
    } else if (diffMinutes > 0) {
      return `${diffMinutes}min atrás`;
    } else {
      return 'Atualizado agora';
    }
  };

  const handleRegionChange = (newRegionCode: string) => {
    setSelectedRegion(newRegionCode);
    // useEffect will automatically reload prices when selectedRegion changes
  };

  // Function to add currency parameter to Kinguin URLs
  const addKinguinCurrencyParam = (url: string, storeName: string): string => {
    if (storeName !== 'Kinguin' || !url) {
      return url;
    }

    try {
      const urlObj = new URL(url);
      const kinguinCurrency = getKinguinCurrencyParam(selectedCurrency);
      urlObj.searchParams.set('currency', kinguinCurrency);
      return urlObj.toString();
    } catch (error) {
      console.warn('Failed to add currency param to Kinguin URL:', error);
      return url;
    }
  };

  // Function to format price with proper currency symbol for display
  const formatPriceDisplay = (priceString: string, storeName: string): string => {
    // Extract numeric value from price string
    const extractNumericValue = (price: string): number => {
      // Remove all non-numeric characters except comma and period
      const cleanPrice = price.replace(/[^\d.,]/g, '');
      
      // For Asian currencies (JPY, KRW, INR), commas are thousands separators, not decimal
      const isAsianCurrency = selectedRegion === 'jp' || selectedRegion === 'kr' || selectedRegion === 'in' || selectedRegion === 'cn';
      
      if (isAsianCurrency) {
        // Remove commas (thousands separators) and treat any period as decimal
        const normalized = cleanPrice.replace(/,/g, '');
        return parseFloat(normalized) || 0;
      } else {
        // Handle different decimal separators for other currencies
        const normalizedPrice = cleanPrice.replace(',', '.');
        return parseFloat(normalizedPrice) || 0;
      }
    };

    // Get currency info based on selected region
    const getCurrencyFormat = (regionCode: string) => {
      const formats: Record<string, { symbol: string, decimals: boolean, separator: string }> = {
        'us': { symbol: '$', decimals: true, separator: '.' },
        'br': { symbol: 'R$', decimals: true, separator: ',' },
        'de': { symbol: '€', decimals: true, separator: ',' },
        'gb': { symbol: '£', decimals: true, separator: '.' },
        'jp': { symbol: '¥', decimals: false, separator: '' },
        'kr': { symbol: '₩', decimals: false, separator: '' },
        'cn': { symbol: '¥', decimals: false, separator: '' },
        'in': { symbol: '₹', decimals: false, separator: '' },
        'ru': { symbol: '₽', decimals: false, separator: '' },
        'mx': { symbol: 'M$', decimals: true, separator: '.' },
        'ca': { symbol: 'C$', decimals: true, separator: '.' },
        'au': { symbol: 'A$', decimals: true, separator: '.' },
      };
      return formats[regionCode] || { symbol: '$', decimals: true, separator: '.' };
    };

    const numericValue = extractNumericValue(priceString);
    const currencyFormat = getCurrencyFormat(selectedRegion);
    
    // For high-value currencies (thousands+), never show decimals
    const isHighValue = numericValue >= 1000;
    const shouldShowDecimals = currencyFormat.decimals && !isHighValue;
    
    // Format the number
    let formattedNumber: string;
    
    if (shouldShowDecimals) {
      // Show decimals for values under 1000 in decimal-supporting currencies
      if (currencyFormat.separator === ',') {
        formattedNumber = numericValue.toFixed(2).replace('.', ',');
      } else {
        formattedNumber = numericValue.toFixed(2);
      }
    } else {
      // No decimals - just whole numbers
      formattedNumber = Math.round(numericValue).toString();
      
      // Add thousands separator only for certain currencies
      if (currencyFormat.decimals && numericValue >= 1000) {
        if (currencyFormat.separator === ',') {
          formattedNumber = formattedNumber.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
        } else {
          formattedNumber = formattedNumber.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        }
      }
    }
    
    // Return formatted price with currency symbol (no space)
    return `${currencyFormat.symbol}${formattedNumber}`;
  };

  // Helper function to extract numeric price from formatted string
  const extractNumericPrice = (priceString: string): number => {
    // Remove currency symbols and spaces, then parse
    const numericString = priceString.replace(/[^\d.,]/g, '').replace(',', '.');
    const price = parseFloat(numericString);
    return isNaN(price) ? 0 : price;
  };

  // Calculate cross-store discount (Kinguin vs Steam)
  const calculateCrossStoreDiscount = (currentPrice: GamePrice, steamPrice: GamePrice | null): { discountPercentage: number, originalPrice: string } | null => {
    if (!steamPrice || currentPrice.store_name === 'Steam') {
      return null;
    }

    // Skip if either price is free or unavailable
    if (currentPrice.price.toLowerCase().includes('gratuito') || 
        currentPrice.price.toLowerCase().includes('indisponível') ||
        steamPrice.price.toLowerCase().includes('gratuito') || 
        steamPrice.price.toLowerCase().includes('indisponível')) {
      return null;
    }

    const currentPriceNum = extractNumericPrice(currentPrice.price);
    const steamPriceNum = extractNumericPrice(steamPrice.price);

    if (currentPriceNum <= 0 || steamPriceNum <= 0 || currentPriceNum >= steamPriceNum) {
      return null;
    }

    const discountPercentage = Math.round(((steamPriceNum - currentPriceNum) / steamPriceNum) * 100);
    
    // Only show discount if it's at least 5%
    if (discountPercentage < 5) {
      return null;
    }

    return {
      discountPercentage,
      originalPrice: steamPrice.price
    };
  };

  if (loading && !refreshing) {
    return (
      <Card className={`p-6 bg-slate-800/60 border-slate-600/50 ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <ShoppingCart className="h-5 w-5 text-slate-400 animate-pulse" />
            <div>
              <div className="text-lg font-semibold text-white">Onde Comprar</div>
              <div className="text-sm text-slate-400">🔍 Buscando preços nas lojas...</div>
            </div>
          </div>
          <div className="text-xs text-slate-500">
            Steam API + Scraping
          </div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={`p-6 bg-slate-800/60 border-slate-600/50 ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <ShoppingCart className="h-5 w-5 text-red-400" />
            <div>
              <div className="text-lg font-semibold text-white">Preços</div>
              <div className="text-sm text-red-400">Erro ao carregar preços</div>
            </div>
          </div>
          <Button
            onClick={() => fetchPrices()}
            size="sm"
            variant="outline"
            className="text-slate-300 border-slate-600 hover:bg-slate-700"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Tentar novamente
          </Button>
        </div>
      </Card>
    );
  }

  if (prices.length === 0) {
    return (
      <Card className={`p-6 bg-slate-800/60 border-slate-600/50 ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <ShoppingCart className="h-5 w-5 text-slate-400" />
            <div>
              <div className="text-lg font-semibold text-white">Onde Comprar</div>
              <div className="text-sm text-slate-400">🔍 Nenhum preço encontrado</div>
              <div className="text-xs text-slate-500 mt-1">Steam API integrado - Tente buscar preços</div>
            </div>
          </div>
          <Button
            onClick={handleForceUpdate}
            size="sm"
            variant="outline"
            disabled={refreshing}
            className="text-slate-300 border-slate-600 hover:bg-slate-700"
          >
            {refreshing ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Buscar preços
          </Button>
        </div>
      </Card>
    );
  }

  const currentRegion = getRegionByCode(selectedRegion);

  return (
    <Card className={`p-6 bg-slate-800/60 border-slate-600/50 ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex flex-col space-y-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              💰 Preços do Jogo
              {currencyInfo && (
                <span className="text-sm font-normal text-muted-foreground">
                  {currencyInfo.flag} {currencyInfo.name}
                </span>
              )}
            </CardTitle>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => fetchPrices(false, true)}
                disabled={loading || refreshing}
                className="gap-1"
              >
                <RefreshCw className={`h-3 w-3 ${loading || refreshing ? 'animate-spin' : ''}`} />
                {refreshing ? 'Atualizando...' : 'Atualizar'}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleForceUpdate}
                disabled={refreshing}
                className="gap-1"
              >
                <RefreshCw className={`h-3 w-3 ${refreshing ? 'animate-spin' : ''}`} />
                Forçar
              </Button>
            </div>
          </div>

          {/* Region Selector */}
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-muted-foreground min-w-fit">
              Região:
            </span>
            <Select value={selectedRegion} onValueChange={handleRegionChange}>
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="Selecione a região" />
              </SelectTrigger>
              <SelectContent>
                {POPULAR_STEAM_REGIONS.map((region) => (
                  <SelectItem key={region.code} value={region.code}>
                    <div className="flex items-center gap-2">
                      <span>{region.flag}</span>
                      <span>{region.name}</span>
                      <span className="text-xs text-muted-foreground">
                        ({region.currency})
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* Cache Status Info */}
        {(needsUpdate || cacheInfo) && (
          <Alert className="mb-4">
            <Clock className="h-4 w-4" />
            <AlertDescription className="flex items-center justify-between">
              <span>
                Última atualização: {formatLastUpdate(lastUpdate)}
                {cacheInfo?.cacheExpired && ' (dados expirados)'}
              </span>
              {needsUpdate && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleForceUpdate}
                  disabled={refreshing}
                  className="ml-2"
                >
                  <RefreshCw className={`h-3 w-3 mr-1 ${refreshing ? 'animate-spin' : ''}`} />
                  Forçar Atualização
                </Button>
              )}
            </AlertDescription>
          </Alert>
        )}

        {/* Error Display */}
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-8">
            <span className="text-muted-foreground">
              Carregando preços para {regionInfo?.name || selectedRegion.toUpperCase()}...
            </span>
          </div>
        )}

        {/* No Prices State */}
        {!loading && !error && prices.length === 0 && (
          <div className="text-center py-8">
            <div className="text-4xl mb-2">🔍</div>
            <div className="text-lg font-medium mb-1">Nenhum preço encontrado</div>
            <div className="text-sm text-muted-foreground mb-4">
              {regionInfo ? 
                `Este jogo pode não estar disponível na região ${regionInfo.name}` :
                'Este jogo pode não estar disponível nesta região'
              }
            </div>
            <Button
              variant="outline"
              onClick={handleForceUpdate}
              disabled={refreshing}
              className="gap-1"
            >
              <RefreshCw className={`h-3 w-3 ${refreshing ? 'animate-spin' : ''}`} />
              Buscar Novamente
            </Button>
          </div>
        )}

        {/* Prices List */}
        {!loading && prices.length > 0 && (
          <div className="space-y-3">
            {(() => {
              // Find Steam price for cross-store discount calculations
              const steamPrice = prices.find(p => p.store_name === 'Steam') || null;
              
              return prices.map((price) => {
                // Calculate cross-store discount if this is not Steam
                const crossStoreDiscount = calculateCrossStoreDiscount(price, steamPrice);
                
                // Determine which discount to show:
                // 1. Cross-store discount (Kinguin vs Steam) takes priority
                // 2. Fall back to store's own discount
                const displayDiscount = crossStoreDiscount || (price.original_price && price.discount_percentage ? {
                  discountPercentage: price.discount_percentage,
                  originalPrice: price.original_price
                } : null);

                return (
                  <motion.div
                    key={price.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: prices.indexOf(price) * 0.1 }}
                    whileHover={{ scale: 1.02 }}
                    className="flex items-center justify-between p-3 bg-slate-700/50 rounded-lg border border-slate-600/50 hover:border-slate-500/50 transition-all duration-200"
                  >
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <div className={`text-sm font-medium ${storeColors[price.store_name] || 'text-white'}`}>
                          {price.store_name}
                        </div>
                        {price.availability !== 'available' && (
                          <Badge 
                            variant="secondary" 
                            className={AVAILABILITY_LABELS[price.availability].color}
                          >
                            {AVAILABILITY_LABELS[price.availability].text}
                          </Badge>
                        )}
                        {crossStoreDiscount && (
                          <Badge 
                            variant="secondary" 
                            className="text-xs bg-blue-900/50 text-blue-300"
                          >
                            vs Steam
                          </Badge>
                        )}
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <div className="text-white font-semibold">
                          {formatPriceDisplay(price.price, price.store_name)}
                        </div>
                        
                        {displayDiscount && (
                          <>
                            <div className="text-xs text-slate-400 line-through">
                              {formatPriceDisplay(displayDiscount.originalPrice, price.store_name)}
                            </div>
                            <Badge 
                              variant="secondary" 
                              className={`text-xs flex items-center gap-1 ${
                                crossStoreDiscount 
                                  ? 'bg-blue-900/50 text-blue-300' 
                                  : 'bg-green-900/50 text-green-300'
                              }`}
                            >
                              <Percent className="h-3 w-3" />
                              {displayDiscount.discountPercentage}% OFF
                              {crossStoreDiscount && (
                                <span className="text-xs opacity-75">vs Steam</span>
                              )}
                            </Badge>
                          </>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Link 
                        href={addKinguinCurrencyParam(price.affiliate_url || price.store_url, price.store_name)} // Add currency param for Kinguin
                        target="_blank" 
                        rel="noopener noreferrer"
                      >
                        <Button
                          size="sm"
                          className={`bg-gradient-to-r ${storeGradients[price.store_name] || 'from-blue-600 to-blue-500'} hover:opacity-90 text-white text-xs border-0`}
                        >
                          {crossStoreDiscount ? `${crossStoreDiscount.discountPercentage}% mais barato` : 
                           price.affiliate_url ? 'Comprar com desconto' : 'Ver na loja'}
                          <ExternalLink className="h-3 w-3 ml-1" />
                        </Button>
                      </Link>
                      {price.affiliate_url && price.store_name === 'Kinguin' && (
                        <span className="text-xs text-orange-300">
                          💰 Link de afiliado
                        </span>
                      )}
                    </div>
                  </motion.div>
                );
              });
            })()}
          </div>
        )}

        {/* Footer Info */}
        {!loading && prices.length > 0 && (
          <div className="mt-4 pt-3 border-t text-center">
            <p className="text-xs text-muted-foreground">
              {prices.length} preço{prices.length !== 1 ? 's' : ''} encontrado{prices.length !== 1 ? 's' : ''} 
              {regionInfo && (
                <span> em {regionInfo.name} ({regionInfo.currency})</span>
              )}
              {lastUpdate && (
                <span> • Atualizado {formatLastUpdate(lastUpdate)}</span>
              )}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 