/**
 * Quick test to check Gamivo website structure
 */
const puppeteer = require('puppeteer');

async function testGamivoStructure() {
  console.log('🔍 Testing Gamivo website structure...');
  
  const browser = await puppeteer.launch({ 
    headless: false, // Show browser for debugging
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
  
  const testGame = 'Elden Ring';
  const searchUrl = `https://www.gamivo.com/search?query=${encodeURIComponent(testGame)}`;
  
  console.log(`🌐 Navigating to: ${searchUrl}`);
  await page.goto(searchUrl, { waitUntil: 'networkidle2', timeout: 30000 });
  
  // Wait for page to load
  await new Promise(resolve => setTimeout(resolve, 5000));
  
  // Check page structure
  const pageInfo = await page.evaluate(() => {
    console.log('📋 Checking page structure...');
    
    // Get page title
    const title = document.title;
    
    // Get all unique class names in the page
    const allElements = document.querySelectorAll('*');
    const classNames = new Set();
    
    allElements.forEach(el => {
      if (el.className && typeof el.className === 'string') {
        el.className.split(' ').forEach(className => {
          if (className.trim()) classNames.add(className.trim());
        });
      }
    });
    
    // Look for potential product containers
    const productContainerCandidates = Array.from(classNames).filter(className => 
      className.toLowerCase().includes('product') ||
      className.toLowerCase().includes('game') ||
      className.toLowerCase().includes('item') ||
      className.toLowerCase().includes('card') ||
      className.toLowerCase().includes('result')
    );
    
    // Count elements with these classes
    const elementCounts = {};
    productContainerCandidates.forEach(className => {
      const count = document.querySelectorAll(`.${className}`).length;
      if (count > 0) {
        elementCounts[className] = count;
      }
    });
    
    // Look for potential price elements
    const priceContainerCandidates = Array.from(classNames).filter(className => 
      className.toLowerCase().includes('price') ||
      className.toLowerCase().includes('cost') ||
      className.toLowerCase().includes('amount')
    );
    
    const priceElementCounts = {};
    priceContainerCandidates.forEach(className => {
      const count = document.querySelectorAll(`.${className}`).length;
      if (count > 0) {
        priceElementCounts[className] = count;
      }
    });
    
    // Sample first few elements that might be products
    const sampleElements = [];
    const selectors = ['.product', '.game', '.item', '.card', '[class*="product"]', '[class*="game"]'];
    
    for (const selector of selectors) {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        // Get first element's structure
        const firstEl = elements[0];
        sampleElements.push({
          selector,
          count: elements.length,
          innerHTML: firstEl.innerHTML.substring(0, 500),
          textContent: firstEl.textContent?.substring(0, 200)
        });
        break; // Just get one sample
      }
    }
    
    return {
      title,
      productContainerCandidates: Object.keys(elementCounts).slice(0, 20), // Top 20
      elementCounts,
      priceContainerCandidates: Object.keys(priceElementCounts).slice(0, 10), // Top 10
      priceElementCounts,
      sampleElements,
      bodyPreview: document.body.innerHTML.substring(0, 1000)
    };
  });
  
  console.log('\n📊 GAMIVO STRUCTURE ANALYSIS:');
  console.log('='.repeat(50));
  console.log(`Page Title: ${pageInfo.title}`);
  console.log('\n🎮 Product Container Candidates:');
  pageInfo.productContainerCandidates.forEach(className => {
    console.log(`  • .${className} (${pageInfo.elementCounts[className]} elements)`);
  });
  
  console.log('\n💰 Price Container Candidates:');
  pageInfo.priceContainerCandidates.forEach(className => {
    console.log(`  • .${className} (${pageInfo.priceElementCounts[className]} elements)`);
  });
  
  console.log('\n📋 Sample Elements:');
  pageInfo.sampleElements.forEach(sample => {
    console.log(`\nSelector: ${sample.selector} (${sample.count} found)`);
    console.log(`Text Content: ${sample.textContent}`);
    console.log(`HTML Preview: ${sample.innerHTML.substring(0, 200)}...`);
  });
  
  console.log('\n🌐 Body HTML Preview:');
  console.log(pageInfo.bodyPreview);
  
  // Keep browser open for manual inspection
  console.log('\n⏳ Browser will stay open for 30 seconds for manual inspection...');
  await new Promise(resolve => setTimeout(resolve, 30000));
  
  await browser.close();
  console.log('✅ Test completed');
}

testGamivoStructure().catch(console.error);