import { NextRequest, NextResponse } from 'next/server';
import { SteamGridDBHeroService } from '@/lib/services/steamGridDBHeroService';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { gameId, gameName } = body;

    if (!gameId || !gameName) {
      return NextResponse.json(
        { error: 'gameId and gameName are required' },
        { status: 400 }
      );
    }

    // Check if hero processing is needed
    const needsProcessing = await SteamGridDBHeroService.needsHeroProcessing(gameId);
    
    if (!needsProcessing) {
      // Return cached hero info
      const cachedInfo = await SteamGridDBHeroService.getCachedHeroInfo(gameId);
      return NextResponse.json({
        success: true,
        cached: true,
        data: cachedInfo
      });
    }

    // Fetch and cache the hero banner
    const result = await SteamGridDBHeroService.fetchAndCacheHeroBanner(gameId, gameName);

    return NextResponse.json({
      success: result.success,
      cached: false,
      data: result.success ? {
        heroUrl: result.heroUrl,
        credits: result.credits,
        status: 'cached'
      } : {
        status: 'failed',
        error: result.error
      }
    });

  } catch (error) {
    console.error('SteamGridDB fetch hero error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch SteamGridDB hero banner',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const gameId = searchParams.get('gameId');

    if (!gameId) {
      return NextResponse.json(
        { error: 'gameId parameter is required' },
        { status: 400 }
      );
    }

    // Check if hero processing is needed
    const needsProcessing = await SteamGridDBHeroService.needsHeroProcessing(gameId);

    if (!needsProcessing) {
      // Return cached hero info
      const cachedInfo = await SteamGridDBHeroService.getCachedHeroInfo(gameId);
      return NextResponse.json({
        success: true,
        cached: true,
        data: cachedInfo
      });
    }

    // Get game name from database for processing
    const gameInfo = await SteamGridDBHeroService.getGameInfo(gameId);
    if (!gameInfo?.name) {
      return NextResponse.json(
        { error: 'Game not found' },
        { status: 404 }
      );
    }

    // Fetch and cache the hero banner
    const result = await SteamGridDBHeroService.fetchAndCacheHeroBanner(gameId, gameInfo.name);

    return NextResponse.json({
      success: result.success,
      cached: false,
      data: result.success ? {
        heroUrl: result.heroUrl,
        credits: result.credits,
        status: 'cached'
      } : {
        status: 'failed',
        error: result.error
      }
    });

  } catch (error) {
    console.error('SteamGridDB get hero info error:', error);
    return NextResponse.json(
      {
        error: 'Failed to get SteamGridDB hero info',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
