/**
 * Test CJ API with approved GAMIVO advertiser ID
 */

async function testApprovedGamivo() {
  console.log('🎯 Testing CJ API with approved GAMIVO access...');
  console.log('='.repeat(60));
  
  const testGame = 'Elden Ring';
  const regionCode = 'us';
  
  // Your approved CJ credentials
  const personalAccessToken = 'xfFMjkz-yyuNksix_dWFMnkgcA';
  const publisherId = '101480418';
  const gamivoAdvertiserId = '6086371';
  
  console.log(`🎮 Game: "${testGame}"`);
  console.log(`🌍 Region: ${regionCode}`);
  console.log(`🆔 Publisher ID: ${publisherId}`);
  console.log(`🎯 GAMIVO Advertiser ID: ${gamivoAdvertiserId}`);
  console.log(`🔑 Access Token: ${personalAccessToken.substring(0, 8)}...`);
  
  try {
    // Test 1: With specific GAMIVO advertiser ID
    console.log('\n📞 TEST 1: Specific GAMIVO Advertiser ID');
    console.log('-'.repeat(40));
    
    const searchParams = new URLSearchParams({
      'website-id': publisherId,
      'keywords': testGame,
      'records-per-page': '10',
      'page-number': '1',
      'serviceable-area': regionCode.toUpperCase(),
      'currency': 'USD',
      'advertiser-ids': gamivoAdvertiserId  // Specific GAMIVO ID
    });

    const apiUrl = `https://product-search.api.cj.com/v2/product-search?${searchParams.toString()}`;
    console.log(`🔍 API URL: ${apiUrl}`);
    
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${personalAccessToken}`,
        'Accept': 'application/json, application/xml, text/xml, */*',
        'User-Agent': 'CriticalPixel/1.0'
      }
    });

    console.log(`📡 Response: ${response.status} ${response.statusText}`);
    console.log(`📋 Content-Type: ${response.headers.get('content-type')}`);
    
    if (response.ok) {
      const contentType = response.headers.get('content-type') || '';
      
      if (contentType.includes('xml')) {
        const xmlText = await response.text();
        console.log('📄 XML Response (first 800 chars):');
        console.log(xmlText.substring(0, 800));
        
        // Simple check for products
        const productCount = (xmlText.match(/<product[^>]*>/g) || []).length;
        console.log(`🎯 Found ${productCount} products in XML`);
        
      } else {
        const jsonData = await response.json();
        console.log('📄 JSON Response:');
        console.log(JSON.stringify(jsonData, null, 2));
        
        if (jsonData.products) {
          console.log(`🎯 Found ${jsonData.products.length} products in JSON`);
        }
      }
    } else {
      const errorText = await response.text();
      console.log(`❌ Error Response: ${errorText}`);
    }
    
    // Test 2: With 'joined' (all approved advertisers)
    console.log('\n📞 TEST 2: All Joined Advertisers');
    console.log('-'.repeat(40));
    
    const searchParams2 = new URLSearchParams({
      'website-id': publisherId,
      'keywords': testGame,
      'records-per-page': '10',
      'page-number': '1',
      'serviceable-area': regionCode.toUpperCase(),
      'currency': 'USD',
      'advertiser-ids': 'joined'  // All approved
    });

    const apiUrl2 = `https://product-search.api.cj.com/v2/product-search?${searchParams2.toString()}`;
    console.log(`🔍 API URL: ${apiUrl2}`);
    
    const response2 = await fetch(apiUrl2, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${personalAccessToken}`,
        'Accept': 'application/json, application/xml, text/xml, */*',
        'User-Agent': 'CriticalPixel/1.0'
      }
    });

    console.log(`📡 Response: ${response2.status} ${response2.statusText}`);
    
    if (response2.ok) {
      const contentType = response2.headers.get('content-type') || '';
      
      if (contentType.includes('xml')) {
        const xmlText = await response2.text();
        console.log('📄 XML Response preview:');
        console.log(xmlText.substring(0, 500));
        
        const productCount = (xmlText.match(/<product[^>]*>/g) || []).length;
        console.log(`🎯 Found ${productCount} products total`);
        
        // Look for GAMIVO specifically
        const gamivoMatches = xmlText.match(/gamivo/gi) || [];
        console.log(`🎮 GAMIVO mentions: ${gamivoMatches.length}`);
        
      } else {
        const jsonData = await response2.json();
        console.log(`🎯 Found ${jsonData.products?.length || 0} products total`);
        
        // Look for GAMIVO
        const gamivoProducts = jsonData.products?.filter(p => 
          p['advertiser-name']?.toLowerCase().includes('gamivo')
        ) || [];
        console.log(`🎮 GAMIVO products: ${gamivoProducts.length}`);
      }
    } else {
      const errorText = await response2.text();
      console.log(`❌ Error Response: ${errorText}`);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
  
  console.log('\n✅ Test completed');
}

testApprovedGamivo().catch(console.error);