import { NextRequest, NextResponse } from 'next/server';
import { HeroMigrationService } from '@/lib/services/heroMigrationService';

// GET - Get migration statistics
export async function GET(request: NextRequest) {
  try {
    const stats = await HeroMigrationService.getMigrationStats();
    
    return NextResponse.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Failed to get migration stats:', error);
    return NextResponse.json(
      { 
        error: 'Failed to get migration statistics',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// POST - Start migration or process specific games
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      action = 'migrate',
      gameIds,
      batchSize = 10,
      delayBetweenBatches = 2000,
      maxConcurrent = 3,
      skipExisting = true
    } = body;

    switch (action) {
      case 'migrate':
        // Start full migration
        console.log('Starting hero banner migration...');
        
        // Run migration in background (don't await)
        HeroMigrationService.runMigration({
          batchSize,
          delayBetweenBatches,
          maxConcurrent,
          skipExisting,
          gameIds
        }).catch(error => {
          console.error('Migration failed:', error);
        });

        return NextResponse.json({
          success: true,
          message: 'Migration started in background',
          data: {
            batchSize,
            delayBetweenBatches,
            maxConcurrent,
            skipExisting,
            gameCount: gameIds ? gameIds.length : 'all'
          }
        });

      case 'process-single':
        if (!body.gameId) {
          return NextResponse.json(
            { error: 'gameId is required for single game processing' },
            { status: 400 }
          );
        }

        const result = await HeroMigrationService.processSingleGame(body.gameId);
        
        return NextResponse.json({
          success: result.success,
          data: result,
          message: result.success 
            ? 'Game processed successfully' 
            : `Processing failed: ${result.error}`
        });

      case 'reset':
        const resetCount = await HeroMigrationService.resetHeroCacheStatus(gameIds);
        
        return NextResponse.json({
          success: true,
          message: `Reset hero cache status for ${resetCount} games`,
          data: { resetCount }
        });

      case 'check-needs-processing':
        if (!body.gameId) {
          return NextResponse.json(
            { error: 'gameId is required for processing check' },
            { status: 400 }
          );
        }

        const needsProcessing = await HeroMigrationService.gameNeedsProcessing(body.gameId);
        
        return NextResponse.json({
          success: true,
          data: { needsProcessing },
          message: needsProcessing 
            ? 'Game needs hero processing' 
            : 'Game already has hero banner or processing not needed'
        });

      default:
        return NextResponse.json(
          { error: `Unknown action: ${action}` },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Migration API error:', error);
    return NextResponse.json(
      { 
        error: 'Migration operation failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
