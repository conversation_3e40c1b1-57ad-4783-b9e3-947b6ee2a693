-- Migration: Steam Price History Table
-- Created: 2025-02-02  
-- Purpose: Store historical Steam price data for tracking price changes over time

-- Create steam_price_history table
CREATE TABLE IF NOT EXISTS steam_price_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    game_id UUID NOT NULL REFERENCES games(id) ON DELETE CASCADE,
    region_code VARCHAR(5) NOT NULL DEFAULT 'br',
    price_cents INTEGER NOT NULL, -- Store price in cents for precise calculations
    currency VARCHAR(10) NOT NULL DEFAULT 'BRL',
    original_price_cents INTEGER, -- Original price before discount in cents
    discount_percentage INTEGER CHECK (discount_percentage >= 0 AND discount_percentage <= 100),
    steam_app_id VARCHAR(20), -- Steam App ID for reference
    price_type VARCHAR(20) DEFAULT 'regular', -- Type: initial, lowest, highest, significant, regular
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Create composite index for fast lookups
    CONSTRAINT unique_game_region_timestamp UNIQUE(game_id, region_code, recorded_at)
);

-- Add the price_type column if it doesn't exist (for existing tables)
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'steam_price_history' AND column_name = 'price_type') THEN
        ALTER TABLE steam_price_history ADD COLUMN price_type VARCHAR(20) DEFAULT 'regular';
    END IF;
END $$;

-- Add region_code column to game_prices table if it doesn't exist
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'game_prices' AND column_name = 'region_code') THEN
        ALTER TABLE game_prices ADD COLUMN region_code VARCHAR(5) DEFAULT 'br';
        -- Update unique constraint to include region_code
        ALTER TABLE game_prices DROP CONSTRAINT IF EXISTS game_prices_game_id_store_name_key;
        ALTER TABLE game_prices ADD CONSTRAINT unique_game_store_region UNIQUE(game_id, store_name, region_code);
    END IF;
END $$;

-- Create indexes for optimal query performance
CREATE INDEX IF NOT EXISTS idx_steam_price_history_game_id ON steam_price_history(game_id);
CREATE INDEX IF NOT EXISTS idx_steam_price_history_region ON steam_price_history(region_code);
CREATE INDEX IF NOT EXISTS idx_steam_price_history_recorded_at ON steam_price_history(recorded_at);
CREATE INDEX IF NOT EXISTS idx_steam_price_history_game_region ON steam_price_history(game_id, region_code);
CREATE INDEX IF NOT EXISTS idx_steam_price_history_game_date ON steam_price_history(game_id, recorded_at);

-- Add RLS (Row Level Security) policies
ALTER TABLE steam_price_history ENABLE ROW LEVEL SECURITY;

-- Policy: Anyone can read price history (public information)
CREATE POLICY "Steam price history is publicly readable" ON steam_price_history
    FOR SELECT USING (true);

-- Policy: Only authenticated users can insert price history data
-- (This should be restricted to admin/system users in production)
CREATE POLICY "Authenticated users can insert price history" ON steam_price_history
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Function to automatically archive significant Steam price changes to history
CREATE OR REPLACE FUNCTION archive_steam_price_to_history()
RETURNS TRIGGER AS $$
DECLARE
    new_price_cents INTEGER;
    current_lowest_price INTEGER;
    current_highest_price INTEGER;
    should_record BOOLEAN := FALSE;
    record_type VARCHAR(20) := 'regular';
BEGIN
    -- Only archive Steam prices to history
    IF NEW.store_name = 'Steam' THEN
        -- Convert new price string to cents
        new_price_cents := CASE 
            WHEN NEW.currency = 'BRL' THEN 
                CAST(REPLACE(REPLACE(NEW.price, 'R$ ', ''), ',', '.') AS DECIMAL) * 100
            WHEN NEW.currency = 'USD' THEN 
                CAST(REPLACE(NEW.price, '$', '') AS DECIMAL) * 100
            WHEN NEW.currency = 'EUR' THEN 
                CAST(REPLACE(NEW.price, '€', '') AS DECIMAL) * 100
            WHEN NEW.currency = 'GBP' THEN 
                CAST(REPLACE(NEW.price, '£', '') AS DECIMAL) * 100
            WHEN NEW.currency = 'JPY' THEN 
                CAST(REPLACE(NEW.price, '¥', '') AS DECIMAL) * 100
            ELSE 
                CAST(REGEXP_REPLACE(NEW.price, '[^0-9.]', '', 'g') AS DECIMAL) * 100
        END;

        -- Get current lowest and highest prices for this game/region
        SELECT 
            MIN(price_cents), 
            MAX(price_cents) 
        INTO current_lowest_price, current_highest_price
        FROM steam_price_history 
        WHERE game_id = NEW.game_id 
            AND region_code = NEW.region_code;

        -- Determine if we should record this price change
        IF current_lowest_price IS NULL OR current_highest_price IS NULL THEN
            -- First price record for this game/region
            should_record := TRUE;
            record_type := 'initial';
        ELSIF new_price_cents < current_lowest_price THEN
            -- New lowest price
            should_record := TRUE;
            record_type := 'lowest';
        ELSIF new_price_cents > current_highest_price THEN
            -- New highest price
            should_record := TRUE;
            record_type := 'highest';
        ELSE
            -- Check if this is a significant price change (more than 5% from last recorded price)
            DECLARE
                last_recorded_price INTEGER;
            BEGIN
                SELECT price_cents INTO last_recorded_price
                FROM steam_price_history 
                WHERE game_id = NEW.game_id 
                    AND region_code = NEW.region_code
                ORDER BY recorded_at DESC
                LIMIT 1;

                IF last_recorded_price IS NOT NULL THEN
                    -- Record if price change is significant (>5%) or if it's been more than 7 days
                    IF ABS(new_price_cents - last_recorded_price) >= (last_recorded_price * 0.05) 
                       OR NOT EXISTS (
                           SELECT 1 FROM steam_price_history 
                           WHERE game_id = NEW.game_id 
                               AND region_code = NEW.region_code 
                               AND recorded_at >= NOW() - INTERVAL '7 days'
                       ) THEN
                        should_record := TRUE;
                        record_type := 'significant';
                    END IF;
                END IF;
            END;
        END IF;

        -- Record the price if it meets our criteria
        IF should_record THEN
            INSERT INTO steam_price_history (
                game_id,
                region_code,
                price_cents,
                currency,
                original_price_cents,
                discount_percentage,
                recorded_at,
                price_type
            ) VALUES (
                NEW.game_id,
                NEW.region_code,
                new_price_cents,
                NEW.currency,
                -- Convert original price to cents if exists
                CASE 
                    WHEN NEW.original_price IS NOT NULL THEN
                        CASE 
                            WHEN NEW.currency = 'BRL' THEN 
                                CAST(REPLACE(REPLACE(NEW.original_price, 'R$ ', ''), ',', '.') AS DECIMAL) * 100
                            WHEN NEW.currency = 'USD' THEN 
                                CAST(REPLACE(NEW.original_price, '$', '') AS DECIMAL) * 100
                            WHEN NEW.currency = 'EUR' THEN 
                                CAST(REPLACE(NEW.original_price, '€', '') AS DECIMAL) * 100
                            WHEN NEW.currency = 'GBP' THEN 
                                CAST(REPLACE(NEW.original_price, '£', '') AS DECIMAL) * 100
                            WHEN NEW.currency = 'JPY' THEN 
                                CAST(REPLACE(NEW.original_price, '¥', '') AS DECIMAL) * 100
                            ELSE 
                                CAST(REGEXP_REPLACE(NEW.original_price, '[^0-9.]', '', 'g') AS DECIMAL) * 100
                        END
                    ELSE NULL
                END,
                NEW.discount_percentage,
                NOW(),
                record_type
            )
            -- Avoid duplicate entries for the same timestamp
            ON CONFLICT (game_id, region_code, recorded_at) DO UPDATE SET
                price_cents = EXCLUDED.price_cents,
                price_type = EXCLUDED.price_type;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically archive Steam prices on update
CREATE TRIGGER trigger_archive_steam_price_history
    AFTER INSERT OR UPDATE ON game_prices
    FOR EACH ROW
    EXECUTE FUNCTION archive_steam_price_to_history();

-- Function to get lowest price ever for a game in a specific region
CREATE OR REPLACE FUNCTION get_lowest_steam_price(p_game_id UUID, p_region_code VARCHAR(5) DEFAULT 'br')
RETURNS TABLE (
    lowest_price_cents INTEGER,
    currency VARCHAR(10),
    recorded_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        sph.price_cents,
        sph.currency,
        sph.recorded_at
    FROM steam_price_history sph
    WHERE sph.game_id = p_game_id 
        AND sph.region_code = p_region_code
    ORDER BY sph.price_cents ASC, sph.recorded_at DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Function to get price history for charting
CREATE OR REPLACE FUNCTION get_steam_price_history_for_chart(
    p_game_id UUID, 
    p_region_code VARCHAR(5) DEFAULT 'br',
    p_days_back INTEGER DEFAULT 90
)
RETURNS TABLE (
    price_cents INTEGER,
    currency VARCHAR(10),
    recorded_at TIMESTAMP WITH TIME ZONE,
    discount_percentage INTEGER,
    price_type VARCHAR(20)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        sph.price_cents,
        sph.currency,
        sph.recorded_at,
        sph.discount_percentage,
        COALESCE(sph.price_type, 'regular') as price_type
    FROM steam_price_history sph
    WHERE sph.game_id = p_game_id 
        AND sph.region_code = p_region_code
        AND sph.recorded_at >= NOW() - INTERVAL '1 day' * p_days_back
    ORDER BY sph.recorded_at ASC;
END;
$$ LANGUAGE plpgsql;

-- Add comments for documentation
COMMENT ON TABLE steam_price_history IS 'Historical Steam price data for tracking price changes over time';
COMMENT ON COLUMN steam_price_history.game_id IS 'Reference to the game in the games table';
COMMENT ON COLUMN steam_price_history.region_code IS 'Steam region code (br, us, eu, gb, jp, etc.)';
COMMENT ON COLUMN steam_price_history.price_cents IS 'Price in cents for precise calculations and comparisons';
COMMENT ON COLUMN steam_price_history.currency IS 'Currency code (BRL, USD, EUR, etc.)';
COMMENT ON COLUMN steam_price_history.original_price_cents IS 'Original price before discount in cents';
COMMENT ON COLUMN steam_price_history.discount_percentage IS 'Discount percentage when recorded (0-100)';
COMMENT ON COLUMN steam_price_history.steam_app_id IS 'Steam App ID for reference';
COMMENT ON COLUMN steam_price_history.recorded_at IS 'When this price was recorded';