-- Simple Steam Price Tracking - Only 3 datapoints per game per currency
-- Original Price, All Time Low, All Time High

-- Drop existing complex system
DROP TRIGGER IF EXISTS trigger_archive_steam_price_history ON game_prices;
DROP FUNCTION IF EXISTS archive_steam_price_to_history();
DROP TABLE IF EXISTS steam_price_history;

-- Create simple steam price tracking table
CREATE TABLE IF NOT EXISTS steam_price_tracking (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    game_id UUID NOT NULL REFERENCES games(id) ON DELETE CASCADE,
    region_code VARCHAR(5) NOT NULL DEFAULT 'br',
    currency VARCHAR(10) NOT NULL DEFAULT 'BRL',
    
    -- The 3 datapoints we track
    original_price_cents INTEGER, -- Regular/original price in cents
    all_time_low_cents INTEGER,   -- Lowest price ever recorded in cents
    all_time_high_cents INTEGER,  -- Highest price ever recorded in cents
    
    -- Timestamps for each datapoint
    original_price_updated TIMESTAMP WITH TIME ZONE,
    all_time_low_updated TIMESTAMP WITH TIME ZONE,
    all_time_high_updated TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- One record per game per region/currency
    CONSTRAINT unique_game_region_currency UNIQUE(game_id, region_code, currency)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_steam_tracking_game_id ON steam_price_tracking(game_id);
CREATE INDEX IF NOT EXISTS idx_steam_tracking_region ON steam_price_tracking(region_code);
CREATE INDEX IF NOT EXISTS idx_steam_tracking_currency ON steam_price_tracking(currency);

-- Enable RLS
ALTER TABLE steam_price_tracking ENABLE ROW LEVEL SECURITY;

-- Public read policy
CREATE POLICY "Steam price tracking is publicly readable" ON steam_price_tracking
    FOR SELECT USING (true);

-- Authenticated insert/update policy
CREATE POLICY "Authenticated users can manage steam tracking" ON steam_price_tracking
    FOR ALL USING (auth.role() = 'authenticated');

-- Function to update steam price tracking when game_prices changes
CREATE OR REPLACE FUNCTION update_steam_price_tracking()
RETURNS TRIGGER AS $$
DECLARE
    new_price_cents INTEGER;
    tracking_record steam_price_tracking%ROWTYPE;
    needs_update BOOLEAN := FALSE;
BEGIN
    -- Only process Steam prices
    IF NEW.store_name != 'Steam' THEN
        RETURN NEW;
    END IF;

    -- Convert price string to cents
    BEGIN
        new_price_cents := CASE 
            WHEN NEW.currency = 'BRL' THEN 
                CAST(REPLACE(REPLACE(NEW.price, 'R$ ', ''), ',', '.') AS DECIMAL) * 100
            WHEN NEW.currency = 'USD' THEN 
                CAST(REPLACE(NEW.price, '$', '') AS DECIMAL) * 100
            WHEN NEW.currency = 'EUR' THEN 
                CAST(REPLACE(REPLACE(NEW.price, '€', ''), ',', '.') AS DECIMAL) * 100
            WHEN NEW.currency = 'GBP' THEN 
                CAST(REPLACE(NEW.price, '£', '') AS DECIMAL) * 100
            WHEN NEW.currency = 'JPY' THEN 
                CAST(REPLACE(NEW.price, '¥', '') AS DECIMAL) * 100
            ELSE 
                CAST(REGEXP_REPLACE(NEW.price, '[^0-9.]', '', 'g') AS DECIMAL) * 100
        END;
    EXCEPTION
        WHEN OTHERS THEN
            RAISE WARNING 'Failed to parse Steam price: % for currency: %', NEW.price, NEW.currency;
            RETURN NEW;
    END;

    -- Get existing tracking record
    SELECT * INTO tracking_record 
    FROM steam_price_tracking 
    WHERE game_id = NEW.game_id 
        AND region_code = COALESCE(NEW.region_code, 'br')
        AND currency = NEW.currency;

    -- If no record exists, create initial one
    IF tracking_record.id IS NULL THEN
        INSERT INTO steam_price_tracking (
            game_id, 
            region_code, 
            currency,
            original_price_cents,
            all_time_low_cents,
            all_time_high_cents,
            original_price_updated,
            all_time_low_updated,
            all_time_high_updated
        ) VALUES (
            NEW.game_id,
            COALESCE(NEW.region_code, 'br'),
            NEW.currency,
            new_price_cents,
            new_price_cents,
            new_price_cents,
            NOW(),
            NOW(),
            NOW()
        );
        RAISE NOTICE 'Steam tracking initialized for game % in % with price %', NEW.game_id, NEW.currency, NEW.price;
        RETURN NEW;
    END IF;

    -- Check if we need to update any of the 3 datapoints
    
    -- 1. Update original price from the original_price field OR current price if no discount
    DECLARE
        original_price_cents_to_store INTEGER;
    BEGIN
        -- Use original_price if available (when there's a discount), otherwise use current price
        IF NEW.original_price IS NOT NULL AND NEW.original_price != '' THEN
            -- Convert original price string to cents
            original_price_cents_to_store := CASE 
                WHEN NEW.currency = 'BRL' THEN 
                    CAST(REPLACE(REPLACE(NEW.original_price, 'R$ ', ''), ',', '.') AS DECIMAL) * 100
                WHEN NEW.currency = 'USD' THEN 
                    CAST(REPLACE(NEW.original_price, '$', '') AS DECIMAL) * 100
                WHEN NEW.currency = 'EUR' THEN 
                    CAST(REPLACE(REPLACE(NEW.original_price, '€', ''), ',', '.') AS DECIMAL) * 100
                WHEN NEW.currency = 'GBP' THEN 
                    CAST(REPLACE(NEW.original_price, '£', '') AS DECIMAL) * 100
                WHEN NEW.currency = 'JPY' THEN 
                    CAST(REPLACE(NEW.original_price, '¥', '') AS DECIMAL) * 100
                ELSE 
                    CAST(REGEXP_REPLACE(NEW.original_price, '[^0-9.]', '', 'g') AS DECIMAL) * 100
            END;
        ELSE
            -- If no original price, use current price (when not on sale)
            original_price_cents_to_store := new_price_cents;
        END IF;
        
        -- Update original price if it's different or higher than current stored value
        IF tracking_record.original_price_cents IS NULL OR 
           original_price_cents_to_store > tracking_record.original_price_cents THEN
            tracking_record.original_price_cents := original_price_cents_to_store;
            tracking_record.original_price_updated := NOW();
            needs_update := TRUE;
        END IF;
    END;
    
    -- 2. Update all-time low if this is lower
    IF tracking_record.all_time_low_cents IS NULL OR new_price_cents < tracking_record.all_time_low_cents THEN
        tracking_record.all_time_low_cents := new_price_cents;
        tracking_record.all_time_low_updated := NOW();
        needs_update := TRUE;
    END IF;
    
    -- 3. Update all-time high if this is higher
    IF tracking_record.all_time_high_cents IS NULL OR new_price_cents > tracking_record.all_time_high_cents THEN
        tracking_record.all_time_high_cents := new_price_cents;
        tracking_record.all_time_high_updated := NOW();
        needs_update := TRUE;
    END IF;

    -- Update record if any datapoint changed
    IF needs_update THEN
        UPDATE steam_price_tracking SET
            original_price_cents = tracking_record.original_price_cents,
            all_time_low_cents = tracking_record.all_time_low_cents,
            all_time_high_cents = tracking_record.all_time_high_cents,
            original_price_updated = tracking_record.original_price_updated,
            all_time_low_updated = tracking_record.all_time_low_updated,
            all_time_high_updated = tracking_record.all_time_high_updated,
            updated_at = NOW()
        WHERE id = tracking_record.id;
        
        RAISE NOTICE 'Steam tracking updated for game % in %: Original=%, Low=%, High=%', 
            NEW.game_id, NEW.currency, tracking_record.original_price_cents, 
            tracking_record.all_time_low_cents, tracking_record.all_time_high_cents;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
CREATE TRIGGER trigger_update_steam_price_tracking
    AFTER INSERT OR UPDATE ON game_prices
    FOR EACH ROW
    EXECUTE FUNCTION update_steam_price_tracking();

-- Function to get steam price data for chart
CREATE OR REPLACE FUNCTION get_steam_price_data_for_chart(
    p_game_id UUID, 
    p_region_code VARCHAR(5) DEFAULT 'br'
)
RETURNS TABLE (
    currency VARCHAR(10),
    original_price_cents INTEGER,
    all_time_low_cents INTEGER,
    all_time_high_cents INTEGER,
    original_price_updated TIMESTAMP WITH TIME ZONE,
    all_time_low_updated TIMESTAMP WITH TIME ZONE,
    all_time_high_updated TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        spt.currency,
        spt.original_price_cents,
        spt.all_time_low_cents,
        spt.all_time_high_cents,
        spt.original_price_updated,
        spt.all_time_low_updated,
        spt.all_time_high_updated
    FROM steam_price_tracking spt
    WHERE spt.game_id = p_game_id 
        AND spt.region_code = p_region_code;
END;
$$ LANGUAGE plpgsql;

-- Add comments
COMMENT ON TABLE steam_price_tracking IS 'Simple Steam price tracking - only 3 datapoints per game per currency';
COMMENT ON COLUMN steam_price_tracking.original_price_cents IS 'Regular/original price in cents (when not on sale)';
COMMENT ON COLUMN steam_price_tracking.all_time_low_cents IS 'Lowest price ever recorded in cents';
COMMENT ON COLUMN steam_price_tracking.all_time_high_cents IS 'Highest price ever recorded in cents';

SELECT 'Simple Steam price tracking system created successfully' as status;