'use client';

import React, { useState, useEffect, useLayoutEffect, useRef, forwardRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
// import { useSupabase } from '@/hooks/useSupabase';
import { ExternalLink, RefreshCw, Monitor, Gamepad2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { MagicCard } from '@/components/ui/magic-card';
import SteamIconSVG from '@/components/ui/icons/gaming/SteamIconSVG';
import PlayStationIcon from '@/components/ui/icons/gaming/PlaystationIcon';
import XboxIcon from '@/components/ui/icons/gaming/XboxIcon';
import NintendoSwitchIconSVG from '@/components/ui/icons/gaming/NintendoSwitchIconSVG';
import KinguinIcon from '@/components/ui/icons/stores/KinguinIcon';
import FanaticalIcon from '@/components/ui/icons/stores/FanaticalIcon';
import { useCurrency } from '@/contexts/currency-context';
import Link from 'next/link';

// Store Icons
const SteamIcon = ({ className }: { className?: string }) => (
  <svg fill="currentColor" className={className} viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
    <path d="M18.102 12.129c0-0 0-0 0-0.001 0-1.564 1.268-2.831 2.831-2.831s2.831 1.268 2.831 2.831c0 1.564-1.267 2.831-2.831 2.831-0 0-0 0-0.001 0h0c-0 0-0 0-0.001 0-1.563 0-2.83-1.267-2.83-2.83 0-0 0-0 0-0.001v0zM24.691 12.135c0-2.081-1.687-3.768-3.768-3.768s-3.768 1.687-3.768 3.768c0 2.081 1.687 3.768 3.768 3.768v0c2.080-0.003 3.765-1.688 3.768-3.767v-0zM10.427 23.76l-1.841-0.762c0.524 1.078 1.611 1.808 2.868 1.808 1.317 0 2.448-0.801 2.93-1.943l0.008-0.021c0.155-0.362 0.246-0.784 0.246-1.226 0-1.757-1.424-3.181-3.181-3.181-0.405 0-0.792 0.076-1.148 0.213l0.022-0.007 1.903 0.787c0.852 0.364 1.439 1.196 1.439 2.164 0 1.296-1.051 2.347-2.347 2.347-0.324 0-0.632-0.066-0.913-0.184l0.015 0.006zM15.974 1.004c-7.857 0.001-14.301 6.046-14.938 13.738l-0.004 0.054 8.038 3.322c0.668-0.462 1.495-0.737 2.387-0.737 0.001 0 0.002 0 0.002 0h-0c0.079 0 0.156 0.005 0.235 0.008l3.575-5.176v-0.074c0.003-3.12 2.533-5.648 5.653-5.648 3.122 0 5.653 2.531 5.653 5.653s-2.531 5.653-5.653 5.653h-0.131l-5.094 3.638c0 0.065 0.005 0.131 0.005 0.199 0 0.001 0 0.002 0 0.003 0 2.342-1.899 4.241-4.241 4.241-2.047 0-3.756-1.451-4.153-3.38l-0.005-0.027-5.755-2.383c1.841 6.345 7.601 10.905 14.425 10.905 8.281 0 14.994-6.713 14.994-14.994s-6.713-14.994-14.994-14.994c-0 0-0.001 0-0.001 0h0z"/>
  </svg>
);

const EpicGamesIcon = ({ className }: { className?: string }) => (
  <svg fill="currentColor" className={className} viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path d="M3 3h18v18H3V3zm2 2v14h14V5H5zm2 2h2v2H7V7zm3 0h2v2h-2V7zm3 0h2v2h-2V7zm3 0h2v2h-2V7z"/>
  </svg>
);

const GOGIcon = ({ className }: { className?: string }) => (
  <svg fill="currentColor" className={className} viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <circle cx="12" cy="12" r="10"/>
    <path d="M8 8h8v8H8z" fill="white"/>
  </svg>
);

const NuuvemIcon = ({ className }: { className?: string }) => (
  <svg fill="currentColor" className={className} viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path d="M12 2L2 7v10l10 5 10-5V7l-10-5zm0 2.2L19 8v8l-7 3.5L5 16V8l7-3.8z"/>
    <circle cx="12" cy="12" r="3"/>
  </svg>
);

const InstantGamingIcon = ({ className }: { className?: string }) => (
  <svg fill="currentColor" className={className} viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path d="M3 12l9-9 9 9-9 9-9-9zm9-6L6 12l6 6 6-6-6-6z"/>
  </svg>
);

const CDKeysIcon = ({ className }: { className?: string }) => (
  <svg fill="currentColor" className={className} viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <rect x="3" y="5" width="18" height="14" rx="2"/>
    <path d="M7 9h2v2H7zm4 0h2v2h-2zm4 0h2v2h-2z" fill="white"/>
  </svg>
);

// Store icon mapping
const STORE_ICONS: { [key: string]: React.ComponentType<{ className?: string }> } = {
  'Steam': SteamIcon,
  'Kinguin': KinguinIcon,
  'Fanatical': FanaticalIcon,
  'Epic Games': EpicGamesIcon,
  'GOG': GOGIcon,
  'Nuuvem': NuuvemIcon,
  'Instant Gaming': InstantGamingIcon,
  'CDKeys': CDKeysIcon,
};

// Store icon colors and backgrounds
const STORE_ICON_STYLES: { [key: string]: { iconColor: string, bgColor: string } } = {
  'Steam': { iconColor: 'text-slate-800', bgColor: 'bg-white' },
  'Kinguin': { iconColor: 'text-yellow-400', bgColor: 'bg-yellow-900' },
  'Fanatical': { iconColor: 'text-white', bgColor: 'bg-[#ff9800]' },
  'Epic Games': { iconColor: 'text-gray-300', bgColor: 'bg-gray-800' },
  'GOG': { iconColor: 'text-purple-400', bgColor: 'bg-purple-900' },
  'Nuuvem': { iconColor: 'text-green-400', bgColor: 'bg-green-900' },
  'Instant Gaming': { iconColor: 'text-orange-500', bgColor: 'bg-orange-900' },
  'CDKeys': { iconColor: 'text-yellow-400', bgColor: 'bg-yellow-900' },
};

// Types
interface GamePrice {
  id: string;
  store_name: string;
  price: string; // Keep as string to match GamePricesWidget
  original_price?: string; // Keep as string to match GamePricesWidget
  discount_percentage?: number;
  store_url: string;
  affiliate_url?: string;
  currency: string;
  availability: 'available' | 'out_of_stock' | 'pre_order';
  region_code: string;
  last_updated: string;
}

interface Props {
  gameId: string;
  selectedRegion?: string; // Make this optional since we'll use currency context
  className?: string;
  onHeightChange?: (height: number) => void;
}

type Platform = 'PC' | 'PlayStation' | 'Nintendo' | 'Xbox';

const PLATFORM_ICONS = {
  PC: SteamIconSVG,
  PlayStation: PlayStationIcon,
  Nintendo: NintendoSwitchIconSVG,
  Xbox: XboxIcon,
};

const PLATFORM_COLORS = {
  PC: 'from-blue-400/20 to-blue-500/20 border-blue-400/30',
  PlayStation: 'from-blue-400/20 to-blue-500/20 border-blue-400/30', 
  Nintendo: 'from-red-500/20 to-red-600/20 border-red-500/30',
  Xbox: 'from-green-500/20 to-green-600/20 border-green-500/30',
};

// Currency symbols mapping
const CURRENCY_SYMBOLS: { [key: string]: string } = {
  USD: '$', EUR: '€', GBP: '£', JPY: '¥', CAD: 'C$', AUD: 'A$',
  BRL: 'R$', RUB: '₽', CNY: '¥', INR: '₹', MXN: 'Mex$',
  CHF: 'CHF', NOK: 'kr', SEK: 'kr', DKK: 'kr', PLN: 'zł',
  CZK: 'Kč', HUF: 'Ft', RON: 'lei', BGN: 'лв', HRK: 'kn',
  UAH: '₴', TRY: '₺', ZAR: 'R', ARS: '$', CLP: '$', COP: '$',
  PEN: 'S/', UYU: '$U', KRW: '₩', THB: '฿', SGD: 'S$',
  MYR: 'RM', PHP: '₱', IDR: 'Rp', VND: '₫', TWD: 'NT$',
  HKD: 'HK$', NZD: 'NZ$', ILS: '₪'
};

// Store gradients
const STORE_GRADIENTS: { [key: string]: string } = {
  Steam: 'from-blue-600 to-blue-800',
  'Epic Games': 'from-gray-800 to-black',
  GOG: 'from-purple-600 to-purple-800',
  Nuuvem: 'from-green-600 to-green-800',
  PlayStation: 'from-blue-500 to-blue-700',
  Xbox: 'from-green-500 to-green-700',
  Nintendo: 'from-red-500 to-red-700',
  'Instant Gaming': 'from-orange-500 to-orange-700',
  Kinguin: 'from-yellow-500 to-yellow-700',
  Fanatical: 'from-orange-600 to-red-600'
};

// Map currency to region codes - same logic as GamePricesWidget
const getCurrencyRegion = (currencyCode: string): string => {
  const currencyToRegion: Record<string, string> = {
    'USD': 'us',
    'EUR': 'de', // Use 'de' for EUR region
    'GBP': 'gb', // Use 'gb' not 'uk' 
    'JPY': 'jp',
    'KRW': 'kr',
    'CNY': 'cn',
    'RUB': 'ru',
    'BRL': 'br',
    'MXN': 'mx',
    'CAD': 'ca',
    'AUD': 'au',
    'INR': 'in',
  };
  return currencyToRegion[currencyCode] || 'us';
};

const GamePricesWidgetInteractive = forwardRef<HTMLDivElement, Props>(function GamePricesWidgetInteractive({ gameId, selectedRegion: propSelectedRegion, className, onHeightChange }, ref) {
  const { selectedCurrency, getKinguinCurrencyParam } = useCurrency();
  const [prices, setPrices] = useState<GamePrice[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedPlatform, setSelectedPlatform] = useState<Platform>('PC');
  const [currentRegion, setCurrentRegion] = useState<string>(() => {
    // Initialize with proper region based on currency context
    if (propSelectedRegion) return propSelectedRegion;
    if (selectedCurrency) return getCurrencyRegion(selectedCurrency);
    return 'us'; // Default to lowercase 'us'
  });
  // const { supabase } = useSupabase();

  // Update selected region when currency changes
  useEffect(() => {
    if (selectedCurrency && !propSelectedRegion) {
      const newRegion = getCurrencyRegion(selectedCurrency);
      console.log(`💱 Currency changed: ${selectedCurrency} -> region: ${newRegion}`);
      setCurrentRegion(newRegion);
    } else if (propSelectedRegion) {
      console.log(`🎯 Using prop region: ${propSelectedRegion}`);
      setCurrentRegion(propSelectedRegion);
    }
  }, [selectedCurrency, propSelectedRegion]);

  // Function to add currency parameter to Kinguin URLs (same as GamePricesWidget)
  const addKinguinCurrencyParam = (url: string, storeName: string): string => {
    if (storeName !== 'Kinguin' || !url) {
      return url;
    }

    try {
      const urlObj = new URL(url);
      const kinguinCurrency = getKinguinCurrencyParam(selectedCurrency);
      urlObj.searchParams.set('currency', kinguinCurrency);
      return urlObj.toString();
    } catch (error) {
      console.warn('Failed to add currency param to Kinguin URL:', error);
      return url;
    }
  };

  // Fetch prices with real API integration and 24-hour auto-refresh
  const fetchPrices = async (forceRefresh = false, enableAutoRefresh = false) => {
    try {
      setIsLoading(true);
      
      // Use currentRegion which is automatically updated from currency context
      const apiRegion = currentRegion.toLowerCase();
      
      // Build endpoint with region and autoRefresh parameters
      const baseEndpoint = `/api/games/${gameId}/prices`;
      const params = new URLSearchParams();
      params.set('region', apiRegion);
      if (enableAutoRefresh) params.set('autoRefresh', 'true');
      
      const endpoint = `${baseEndpoint}?${params.toString()}`;
      const method = forceRefresh ? 'POST' : 'GET';
      const body = forceRefresh ? JSON.stringify({ region: apiRegion, multiRegion: false }) : undefined;
      
      console.log(`🎮 Interactive widget: Fetching prices for gameId="${gameId}", region="${apiRegion}"`);
      if (enableAutoRefresh) {
        console.log(`⚡ Auto-refresh enabled for 24-hour cache management`);
      }
      
      const response = await fetch(endpoint, { 
        method,
        headers: forceRefresh ? { 'Content-Type': 'application/json' } : {},
        body
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`❌ Price fetch failed (${response.status}):`, errorText);
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log(`💰 Received ${data.count || 0} prices for interactive widget`);
      
      // Convert API response to component format - preserve original price strings
      const apiPrices = data.prices || [];
      const convertedPrices: GamePrice[] = apiPrices.map((price: any, index: number) => ({
        id: price.id || `price-${index}`,
        store_name: price.store_name,
        price: price.price?.toString() || '0', // Keep original string format
        original_price: price.original_price?.toString(), // Keep original string format
        discount_percentage: price.discount_percentage,
        store_url: price.store_url || '#',
        affiliate_url: price.affiliate_url,
        currency: price.currency,
        availability: price.availability || 'available',
        region_code: price.region_code,
        last_updated: price.last_updated || new Date().toISOString()
      }));
      
      // Filter prices by platform
      const platformFilteredPrices = convertedPrices.filter(price => {
        switch (selectedPlatform) {
          case 'PC':
            return ['Steam', 'Epic Games', 'GOG', 'Kinguin', 'Fanatical', 'Instant Gaming', 'Nuuvem'].includes(price.store_name);
          case 'PlayStation':
            return ['PlayStation Store', 'PlayStation', 'CDKeys'].includes(price.store_name);
          case 'Nintendo':
            return ['Nintendo eShop', 'Nintendo', 'GameStop'].includes(price.store_name);
          case 'Xbox':
            return ['Microsoft Store', 'Xbox', 'CDKeys'].includes(price.store_name);
          default:
            return true;
        }
      });
      
      console.log(`🎮 Platform filtered: ${platformFilteredPrices.length} prices for ${selectedPlatform}`);
      setPrices(platformFilteredPrices);
      
      // Track if auto-refresh was triggered
      if (data.cacheInfo?.autoRefreshTriggered) {
        console.log(`⚡ Interactive widget: Auto-refresh triggered (cache was ${data.cacheInfo.hoursSinceUpdate}h old)`);
      }
      
    } catch (error) {
      console.error('❌ Error fetching prices in interactive widget:', error);
      // Fallback to empty prices on error
      setPrices([]);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Initial load with auto-refresh enabled for 24-hour cache refresh
  useEffect(() => {
    if (gameId && currentRegion) {
      console.log(`🔍 Interactive widget useEffect: gameId="${gameId}", currentRegion="${currentRegion}", selectedCurrency="${selectedCurrency}"`);

      // Don't fetch if we're still on default 'us' region but have a different selectedCurrency
      if (currentRegion === 'us' && selectedCurrency && selectedCurrency !== 'USD') {
        console.log(`⏳ Waiting for currency context to update region from 'us' to proper region for ${selectedCurrency}`);
        return;
      }

      // Enable auto-refresh to trigger background updates if cache is >24h old
      fetchPrices(false, true);
    }
  }, [gameId, currentRegion, selectedPlatform, selectedCurrency]);

  // Height change notification effect - use useLayoutEffect for immediate DOM measurement
  useLayoutEffect(() => {
    if (ref && typeof ref === 'object' && ref.current && onHeightChange) {
      const element = ref.current;
      const height = element.getBoundingClientRect().height;
      onHeightChange(height);
    }
  }, [prices, isLoading, selectedPlatform, onHeightChange, ref]);

  // Function to format price with proper currency symbol for display (EXACT copy from GamePricesWidget)
  const formatPriceDisplay = (priceString: string, storeName: string): string => {
    // Extract numeric value from price string
    const extractNumericValue = (price: string): number => {
      // Remove all non-numeric characters except comma and period
      const cleanPrice = price.replace(/[^\d.,]/g, '');
      
      // For Asian currencies (JPY, KRW, INR), commas are thousands separators, not decimal
      const isAsianCurrency = currentRegion === 'jp' || currentRegion === 'kr' || currentRegion === 'in' || currentRegion === 'cn';
      
      if (isAsianCurrency) {
        // Remove commas (thousands separators) and treat any period as decimal
        const normalized = cleanPrice.replace(/,/g, '');
        return parseFloat(normalized) || 0;
      } else {
        // Handle different decimal separators for other currencies
        const normalizedPrice = cleanPrice.replace(',', '.');
        return parseFloat(normalizedPrice) || 0;
      }
    };

    // Get currency info based on selected region
    const getCurrencyFormat = (regionCode: string) => {
      const formats: Record<string, { symbol: string, decimals: boolean, separator: string }> = {
        'us': { symbol: '$', decimals: true, separator: '.' },
        'br': { symbol: 'R$', decimals: true, separator: ',' },
        'de': { symbol: '€', decimals: true, separator: ',' },
        'gb': { symbol: '£', decimals: true, separator: '.' },
        'jp': { symbol: '¥', decimals: false, separator: '' },
        'kr': { symbol: '₩', decimals: false, separator: '' },
        'cn': { symbol: '¥', decimals: false, separator: '' },
        'in': { symbol: '₹', decimals: false, separator: '' },
        'ru': { symbol: '₽', decimals: false, separator: '' },
        'mx': { symbol: 'M$', decimals: true, separator: '.' },
        'ca': { symbol: 'C$', decimals: true, separator: '.' },
        'au': { symbol: 'A$', decimals: true, separator: '.' },
      };
      return formats[regionCode] || { symbol: '$', decimals: true, separator: '.' };
    };

    const numericValue = extractNumericValue(priceString);
    const currencyFormat = getCurrencyFormat(currentRegion);
    
    // For high-value currencies (thousands+), never show decimals
    const isHighValue = numericValue >= 1000;
    const shouldShowDecimals = currencyFormat.decimals && !isHighValue;
    
    // Format the number
    let formattedNumber: string;
    
    if (shouldShowDecimals) {
      // Show decimals for values under 1000 in decimal-supporting currencies
      if (currencyFormat.separator === ',') {
        formattedNumber = numericValue.toFixed(2).replace('.', ',');
      } else {
        formattedNumber = numericValue.toFixed(2);
      }
    } else {
      // No decimals - just whole numbers
      formattedNumber = Math.round(numericValue).toString();
      
      // Add thousands separator only for certain currencies
      if (currencyFormat.decimals && numericValue >= 1000) {
        if (currencyFormat.separator === ',') {
          formattedNumber = formattedNumber.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
        } else {
          formattedNumber = formattedNumber.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        }
      }
    }
    
    // Return formatted price with currency symbol (no space)
    return `${currencyFormat.symbol}${formattedNumber}`;
  };

  // Get Steam price (always show full price, not discounted) and deals
  const steamPrice = prices.find(p => p.store_name === 'Steam');
  const steamFullPrice = steamPrice ? {
    ...steamPrice,
    // Always show original/full price for Steam, not discounted price
    price: steamPrice.original_price || steamPrice.price,
    // Clear discount info since we're showing full price
    original_price: undefined,
    discount_percentage: undefined
  } : null;

  // Helper function to extract numeric price for sorting
  const extractNumericPrice = (priceString: string): number => {
    const cleanPrice = priceString.replace(/[^\d.,]/g, '');
    const normalizedPrice = cleanPrice.replace(',', '.');
    return parseFloat(normalizedPrice) || 0;
  };

  // Get deals (including discounted Steam) sorted by best price
  const dealPrices = prices
    .filter(p => {
      // Include non-Steam prices
      if (p.store_name !== 'Steam') return true;
      // Include Steam only if it has a discount (original_price exists and is higher)
      if (p.store_name === 'Steam' && p.original_price && extractNumericPrice(p.original_price) > extractNumericPrice(p.price)) return true;
      return false;
    })
    .sort((a, b) => extractNumericPrice(a.price) - extractNumericPrice(b.price))
    .slice(0, 3);

  const platformConfig = {
    PC: {
      icon: SteamIconSVG,
      theme: 'from-blue-500/20 to-cyan-500/20',
      accentColor: 'border-blue-500/30',
      textColor: 'text-blue-400'
    },
    PlayStation: {
      icon: PlayStationIcon,
      theme: 'from-blue-600/20 to-purple-600/20',
      accentColor: 'border-blue-600/30',
      textColor: 'text-blue-400'
    },
    Nintendo: {
      icon: NintendoSwitchIconSVG,
      theme: 'from-red-500/20 to-orange-500/20',
      accentColor: 'border-red-500/30',
      textColor: 'text-red-400'
    },
    Xbox: {
      icon: XboxIcon,
      theme: 'from-green-500/20 to-emerald-500/20',
      accentColor: 'border-green-500/30',
      textColor: 'text-green-400'
    }
  };

  const config = platformConfig[selectedPlatform];
  
  // Helper function to calculate discount percentage
  const calculateDiscountPercent = (dealPrice: GamePrice): number => {
    // For Steam, use its own discount percentage if available
    if (dealPrice.store_name === 'Steam' && dealPrice.discount_percentage) {
      return dealPrice.discount_percentage;
    }
    
    // For other stores, calculate vs Steam full price
    if (!steamFullPrice) return 0;
    const steamPriceNum = extractNumericPrice(steamFullPrice.price);
    const dealPriceNum = extractNumericPrice(dealPrice.price);
    const discount = Math.round(((steamPriceNum - dealPriceNum) / steamPriceNum) * 100);
    return Math.max(0, discount); // Ensure no negative discounts
  };

  if (isLoading) {
    return (
      <div className={cn("relative w-full", className)}>
        <div className="bg-slate-800/40 border border-slate-600/30 rounded-lg p-4">
          <div className="flex items-center justify-center h-20">
            <RefreshCw className="w-4 h-4 animate-spin text-violet-400" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("relative w-full", className)}>
      <div ref={ref} className="bg-slate-800/40 border border-slate-600/30 rounded-lg p-3 hover:bg-slate-800/60 transition-all duration-200">
        {/* Platform Selector */}
        <div className="flex gap-1 mb-4 p-1 bg-slate-800/50 rounded-lg">
          {(['PC', 'PlayStation', 'Nintendo', 'Xbox'] as Platform[]).map((platform) => {
            const IconComponent = PLATFORM_ICONS[platform];
            const isSelected = selectedPlatform === platform;
            
            return (
              <button
                key={platform}
                onClick={() => setSelectedPlatform(platform)}
                className={`flex-1 h-8 p-1 flex items-center justify-center rounded transition-all ${
                  isSelected 
                    ? 'bg-slate-700 text-blue-400' 
                    : 'text-slate-400 hover:text-slate-200'
                }`}
                title={platform}
              >
                <IconComponent className="w-4 h-4" />
              </button>
            );
          })}
        </div>

        {/* Steam Full Price - Always at top */}
        <div className="my-4">
          {steamFullPrice ? (
            <Link 
              href={addKinguinCurrencyParam(steamFullPrice.affiliate_url || steamFullPrice.store_url, steamFullPrice.store_name)}
              target="_blank"
              rel="noopener noreferrer"
              className="block"
            >
              <div className="flex items-center justify-between pr-2 bg-slate-800/30 rounded border border-slate-700/30 group cursor-pointer h-8 relative">
                <div className="relative rounded-lg flex items-center">
                  <div className="h-10 w-10 rounded-lg bg-white flex items-center justify-center relative z-20 transition-all duration-300 group-hover:scale-105 group-hover:drop-shadow-lg -my-1">
                    <SteamIcon className="h-full w-full text-slate-800 transition-all duration-300 group-hover:drop-shadow-[0_0_8px_rgba(30,41,59,0.8)]" />
                  </div>
                  <span className="text-xs font-mono text-slate-400 px-2 py-1 relative z-10 transition-colors duration-300 group-hover:text-slate-200">
                    {steamFullPrice.store_name}
                  </span>
                </div>
                <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 ease-out bg-gradient-to-r from-blue-500/60 via-blue-500/40 via-blue-500/20 to-transparent"></div>
                <span className="text-lg font-bold font-mono text-slate-200 transition-colors duration-300 group-hover:text-blue-200">
                  {formatPriceDisplay(steamFullPrice.price, steamFullPrice.store_name)}
                </span>
              </div>
            </Link>
          ) : (
            <div className="text-sm font-mono text-slate-500">
              // no Steam price for {selectedPlatform}
              <div className="text-xs text-slate-600 mt-1">
                Region: {currentRegion.toUpperCase()} | Game: {gameId}
              </div>
            </div>
          )}
        </div>

        {/* Deals - Non-Steam prices with discount vs Steam */}
        <div className="my-4">
          <div className={`text-xs font-mono my-6 ${config.textColor}`}>
            // Deals
          </div>
          <div className="space-y-6">
            {dealPrices.length > 0 ? dealPrices.map((price, index) => {
              const discountPercent = calculateDiscountPercent(price);
              const isAffiliate = price.store_name.toLowerCase().includes('kinguin') || price.store_name.toLowerCase().includes('cdkeys') || price.store_name.toLowerCase().includes('fanatical');
              const isSteamDeal = price.store_name === 'Steam';
              const StoreIconComponent = STORE_ICONS[price.store_name];
              
              return (
                <Link 
                  key={price.id}
                  href={addKinguinCurrencyParam(price.affiliate_url || price.store_url, price.store_name)}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="block"
                >
                  <div className="flex items-center justify-between bg-slate-800/30 rounded border border-slate-700/30 group cursor-pointer h-8 relative">
                    <div className="relative rounded-lg flex items-center">
                      {StoreIconComponent ? (
                        <div className={`h-10 w-10 rounded-lg flex items-center justify-center relative z-20 transition-all duration-300 group-hover:scale-105 group-hover:drop-shadow-lg -my-1 ${
                          STORE_ICON_STYLES[price.store_name]?.bgColor || 'bg-slate-700/50'
                        }`}>
                          <StoreIconComponent className={`h-full w-full transition-all duration-300 ${
                            STORE_ICON_STYLES[price.store_name]?.iconColor || 'text-slate-400'
                          } ${
                            (() => {
                              const storeName = price.store_name;
                              const iconColor = STORE_ICON_STYLES[storeName]?.iconColor;
                              if (storeName === 'Fanatical') return 'group-hover:drop-shadow-[0_0_8px_rgba(255,152,0,0.8)]';
                              if (iconColor?.includes('orange')) return 'group-hover:drop-shadow-[0_0_8px_rgba(251,146,60,0.6)]';
                              if (iconColor?.includes('purple')) return 'group-hover:drop-shadow-[0_0_8px_rgba(168,85,247,0.6)]';
                              if (iconColor?.includes('green')) return 'group-hover:drop-shadow-[0_0_8px_rgba(34,197,94,0.6)]';
                              if (iconColor?.includes('yellow')) return 'group-hover:drop-shadow-[0_0_8px_rgba(250,204,21,0.6)]';
                              if (iconColor?.includes('gray')) return 'group-hover:drop-shadow-[0_0_8px_rgba(156,163,175,0.6)]';
                              if (iconColor?.includes('slate-800')) return 'group-hover:drop-shadow-[0_0_8px_rgba(30,41,59,0.8)]';
                              return 'group-hover:drop-shadow-[0_0_8px_rgba(100,116,139,0.6)]';
                            })()
                          }`} />
                        </div>
                      ) : (
                        <div className="h-10 w-10 rounded-lg bg-slate-700 flex items-center justify-center relative z-20 transition-all duration-300 group-hover:scale-105 group-hover:drop-shadow-lg -my-1">
                          <span className="text-xs font-mono text-slate-400">
                            {index + 1}
                          </span>
                        </div>
                      )}
                      <span className="text-xs font-mono text-slate-300 px-2 py-1 relative z-10 transition-colors duration-300 group-hover:text-slate-200">
                        {price.store_name}
                      </span>
                    </div>
                    <div className={`absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 ease-out ${
                      (() => {
                        const storeName = price.store_name;
                        const iconColor = STORE_ICON_STYLES[storeName]?.iconColor;
                        if (storeName === 'Fanatical') return 'bg-gradient-to-r from-[#ff9800]/60 via-[#ff9800]/40 via-[#ff9800]/20 to-transparent';
                        if (iconColor?.includes('orange')) return 'bg-gradient-to-r from-orange-500/60 via-orange-500/40 via-orange-500/20 to-transparent';
                        if (iconColor?.includes('purple')) return 'bg-gradient-to-r from-purple-500/60 via-purple-500/40 via-purple-500/20 to-transparent';
                        if (iconColor?.includes('green')) return 'bg-gradient-to-r from-green-500/60 via-green-500/40 via-green-500/20 to-transparent';
                        if (iconColor?.includes('yellow')) return 'bg-gradient-to-r from-yellow-500/60 via-yellow-500/40 via-yellow-500/20 to-transparent';
                        if (iconColor?.includes('gray')) return 'bg-gradient-to-r from-gray-500/60 via-gray-500/40 via-gray-500/20 to-transparent';
                        if (iconColor?.includes('slate-800')) return 'bg-gradient-to-r from-blue-500/60 via-blue-500/40 via-blue-500/20 to-transparent';
                        return 'bg-gradient-to-r from-slate-500/60 via-slate-500/40 via-slate-500/20 to-transparent';
                      })()
                    }`}></div>
                    <div className="flex items-center h-full">
                      <span className="text-sm font-bold font-mono text-slate-200 px-2 flex-1">
                        {formatPriceDisplay(price.price, price.store_name)}
                      </span>
                      {discountPercent > 0 && (
                        <span className={`flex items-center justify-center h-full text-[10px] font-medium px-2 -mr-px -mt-px -mb-px ${
                          isSteamDeal 
                            ? 'bg-gradient-to-r from-blue-500/20 to-cyan-500/20 border-l border-blue-500/30 text-blue-300'
                            : 'bg-gradient-to-r from-green-500/20 to-emerald-500/20 border-l border-green-500/30 text-green-300'
                        }`}>
                          -{discountPercent}%
                        </span>
                      )}
                    </div>
                  </div>
                </Link>
              );
            }) : (
              <div className="text-sm font-mono text-slate-500 text-center p-2">
                // no deals available for {selectedPlatform}
                <div className="text-xs text-slate-600 mt-1">
                  {steamFullPrice ? 'Only Steam price available' : 'No price data found'}
                </div>
              </div>
            )}
          </div>
        </div>

        {isLoading && (
          <div className="absolute inset-0 bg-slate-900/50 backdrop-blur-sm rounded-lg flex items-center justify-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-400"></div>
          </div>
        )}
      </div>
    </div>
  );
});

export default GamePricesWidgetInteractive;