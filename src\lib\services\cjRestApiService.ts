/**
 * CJ Affiliate REST API Service
 * 
 * This service integrates with Commission Junction's Product Search REST API
 * to fetch prices from affiliate partners like GAMIVO, Fanatical, etc.
 */

export interface CJGamePrice {
  store_name: string;
  price: number;
  original_price?: number;
  discount_percentage?: number;
  store_url: string;
  affiliate_url: string;
  currency: string;
  availability: 'available' | 'out_of_stock' | 'pre_order';
  region: string;
  advertiser_name: string;
  product_name: string;
}

interface CJProductSearchResponse {
  products?: Array<{
    'advertiser-name': string;
    'buy-url': string;
    'catalog-id': string;
    'currency': string;
    'description': string;
    'image-url': string;
    'in-stock': string;
    'name': string;
    'price': string;
    'retail-price': string;
    'sale-price': string;
    'sku': string;
    'upc': string;
  }>;
  'records-returned'?: string;
  'total-matched'?: string;
}

export class CJRestApiService {
  private personalAccessToken: string;
  private publisherId: string;
  private baseUrl: string;
  private requestTimeout: number;
  private debugMode: boolean;

  constructor() {
    this.personalAccessToken = process.env.CJ_PERSONAL_ACCESS_TOKEN || process.env.CJ_DEVELOPER_KEY || '';
    this.publisherId = process.env.CJ_PUBLISHER_ID || process.env.CJ_WEBSITE_ID || '';
    this.baseUrl = 'https://product-search.api.cj.com/v2/product-search';
    this.requestTimeout = parseInt(process.env.CJ_REQUEST_TIMEOUT || '30000');
    
    const forceRealData = process.env.CJ_FORCE_REAL_DATA === 'true';
    this.debugMode = forceRealData ? false : (process.env.NODE_ENV === 'development');
    
    if (forceRealData) {
      console.log('🔥 CJ REST API: FORCED REAL DATA MODE - Mock data disabled');
    }

    if (this.debugMode) {
      console.log('🔧 CJ REST API Configuration:', {
        hasPersonalAccessToken: !!this.personalAccessToken,
        hasPublisherId: !!this.publisherId,
        baseUrl: this.baseUrl,
        debugMode: this.debugMode
      });
    }
  }

  /**
   * Check if CJ API is properly configured
   */
  isConfigured(): boolean {
    return !!(this.personalAccessToken && this.publisherId);
  }

  /**
   * Get currency for region
   */
  private getRegionCurrency(regionCode: string): string {
    const regionCurrencies: Record<string, string> = {
      'us': 'USD', 'ca': 'CAD', 'mx': 'MXN',
      'br': 'BRL', 'ar': 'ARS',
      'gb': 'GBP', 'de': 'EUR', 'fr': 'EUR', 'es': 'EUR', 'it': 'EUR',
      'jp': 'JPY', 'kr': 'KRW', 'cn': 'CNY', 'in': 'INR',
      'au': 'AUD', 'nz': 'NZD',
      'ru': 'RUB', 'tr': 'TRY'
    };
    return regionCurrencies[regionCode.toLowerCase()] || 'USD';
  }

  /**
   * Search for games in CJ affiliate network using REST API
   */
  async searchGames(gameName: string, regionCode: string = 'us'): Promise<CJGamePrice[]> {
    if (!this.isConfigured()) {
      console.error('❌ CJ REST API not configured');
      return [];
    }

    if (this.debugMode) {
      console.log(`🧪 CJ REST API: Mock mode - returning sample data for "${gameName}"`);
      return this.getMockData(gameName, regionCode);
    }

    try {
      console.log(`🚀 CJ REST API: Searching for "${gameName}" in region ${regionCode}`);
      
      // Build REST API URL with parameters
      const baseParams = {
        'website-id': this.publisherId,
        'keywords': gameName,
        'records-per-page': '10',
        'page-number': '1',
        'serviceable-area': regionCode.toUpperCase(),
        'currency': this.getRegionCurrency(regionCode)
      };

      // Add advertiser IDs if specified, otherwise use 'joined'
      const gamivoAdvertiserId = process.env.CJ_GAMIVO_ADVERTISER_ID;
      if (gamivoAdvertiserId) {
        baseParams['advertiser-ids'] = gamivoAdvertiserId;
        console.log(`🎯 Using specific GAMIVO advertiser ID: ${gamivoAdvertiserId}`);
      } else {
        baseParams['advertiser-ids'] = 'joined';
        console.log(`🌍 Using 'joined' for all approved advertisers`);
      }

      const searchParams = new URLSearchParams(baseParams);

      const apiUrl = `${this.baseUrl}?${searchParams.toString()}`;
      console.log(`🔍 CJ REST API: ${apiUrl}`);
      
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.personalAccessToken}`,
          'Accept': 'application/json, application/xml, text/xml, */*',
          'User-Agent': 'CriticalPixel/1.0'
        },
        signal: AbortSignal.timeout(this.requestTimeout)
      });

      console.log(`📡 CJ REST API Response: ${response.status} ${response.statusText}`);
      console.log(`📋 Response Headers:`, Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`❌ CJ REST API error (${response.status}): ${errorText}`);
        
        if (response.status === 406) {
          console.log('🔍 406 Not Acceptable - Possible causes:');
          console.log('   • Request format not matching API expectations');
          console.log('   • Missing required parameters');
          console.log('   • Content-Type/Accept header mismatch');
          console.log('   • Let\'s try different parameter combinations...');
        }
        
        return [];
      }

      // Handle both JSON and XML responses
      const contentType = response.headers.get('content-type') || '';
      console.log(`📥 CJ REST API: Response received (${contentType})`);
      
      let data: CJProductSearchResponse;
      
      if (contentType.includes('xml')) {
        // Handle XML response
        const xmlText = await response.text();
        console.log(`📄 CJ XML Response preview:`, xmlText.substring(0, 500));
        data = this.parseXMLResponse(xmlText);
      } else {
        // Handle JSON response
        data = await response.json();
        console.log(`📄 CJ JSON Response:`, JSON.stringify(data, null, 2).substring(0, 500));
      }
      
      if (!data.products || data.products.length === 0) {
        console.log('⚠️ CJ REST API: No products found');
        return [];
      }

      console.log(`✅ CJ REST API: Found ${data.products.length} products`);
      
      return this.parseProducts(data.products, regionCode);

    } catch (error) {
      console.error('❌ CJ REST API request failed:', error);
      return [];
    }
  }

  /**
   * Parse products from CJ API response
   */
  private parseProducts(products: CJProductSearchResponse['products'], regionCode: string): CJGamePrice[] {
    if (!products) return [];

    const gamePrice: CJGamePrice[] = [];

    products.forEach((product, index) => {
      try {
        const price = parseFloat(product['sale-price'] || product['price'] || '0');
        const originalPrice = parseFloat(product['retail-price'] || '0');
        
        let discountPercentage: number | undefined;
        if (originalPrice > price && originalPrice > 0) {
          discountPercentage = Math.round(((originalPrice - price) / originalPrice) * 100);
        }

        const gameItem: CJGamePrice = {
          store_name: this.normalizeStoreName(product['advertiser-name']),
          price: price,
          original_price: originalPrice > 0 ? originalPrice : undefined,
          discount_percentage: discountPercentage,
          store_url: product['buy-url'],
          affiliate_url: product['buy-url'], // CJ URLs are already affiliate URLs
          currency: product['currency'],
          availability: product['in-stock'] === 'true' ? 'available' : 'out_of_stock',
          region: regionCode,
          advertiser_name: product['advertiser-name'],
          product_name: product['name']
        };

        gamePrice.push(gameItem);
        console.log(`📦 CJ Product ${index + 1}: ${gameItem.store_name} - ${gameItem.currency} ${gameItem.price}`);

      } catch (error) {
        console.error(`❌ Error parsing product ${index + 1}:`, error);
      }
    });

    return gamePrice;
  }

  /**
   * Parse XML response from CJ API
   */
  private parseXMLResponse(xmlText: string): CJProductSearchResponse {
    // Simple XML parsing for CJ Product Search response
    // In a real implementation, you'd use a proper XML parser like xml2js
    const products: CJProductSearchResponse['products'] = [];
    
    try {
      // Extract product blocks from XML
      const productMatches = xmlText.match(/<product[^>]*>[\s\S]*?<\/product>/g) || [];
      
      productMatches.forEach(productXml => {
        const product: any = {};
        
        // Extract key fields using regex (basic XML parsing)
        const fields = [
          'advertiser-name', 'buy-url', 'catalog-id', 'currency',
          'description', 'image-url', 'in-stock', 'name',
          'price', 'retail-price', 'sale-price', 'sku', 'upc'
        ];
        
        fields.forEach(field => {
          const match = productXml.match(new RegExp(`<${field}[^>]*>([^<]*)<\/${field}>`));
          if (match) {
            product[field] = match[1];
          }
        });
        
        if (product.name && product.price) {
          products.push(product);
        }
      });
      
      console.log(`📦 Parsed ${products.length} products from XML`);
      
    } catch (error) {
      console.error('❌ XML parsing error:', error);
    }
    
    return { products };
  }

  /**
   * Normalize store names
   */
  private normalizeStoreName(advertiserName: string): string {
    const storeMap: Record<string, string> = {
      'gamivo.com': 'Gamivo',
      'fanatical.com': 'Fanatical',
      'humble bundle': 'Humble Bundle',
      'gamesplanet': 'GamesPlanet',
      'green man gaming': 'Green Man Gaming',
      'cdkeys.com': 'CDKeys',
      'voidu.com': 'Voidu'
    };

    const lowerName = advertiserName.toLowerCase();
    for (const [key, value] of Object.entries(storeMap)) {
      if (lowerName.includes(key)) {
        return value;
      }
    }

    return advertiserName;
  }

  /**
   * Mock data for development
   */
  private getMockData(gameName: string, regionCode: string): CJGamePrice[] {
    const currency = this.getRegionCurrency(regionCode);
    const gameHash = gameName.split('').reduce((a, b) => a + b.charCodeAt(0), 0);
    const basePrice = 20 + (gameHash % 40); // $20-60 range
    
    return [
      {
        store_name: 'Gamivo',
        price: basePrice * 0.8,
        original_price: basePrice,
        discount_percentage: 20,
        store_url: `https://www.gamivo.com/search?query=${encodeURIComponent(gameName)}`,
        affiliate_url: `https://www.gamivo.com/search?query=${encodeURIComponent(gameName)}`,
        currency: currency,
        availability: 'available',
        region: regionCode,
        advertiser_name: 'Gamivo.com',
        product_name: `${gameName} (Mock)`
      },
      {
        store_name: 'Fanatical',
        price: basePrice * 0.75,
        original_price: basePrice,
        discount_percentage: 25,
        store_url: `https://www.fanatical.com/en/search?search=${encodeURIComponent(gameName)}`,
        affiliate_url: `https://www.fanatical.com/en/search?search=${encodeURIComponent(gameName)}`,
        currency: currency,
        availability: 'available',
        region: regionCode,
        advertiser_name: 'Fanatical.com',
        product_name: `${gameName} (Mock)`
      }
    ];
  }
}

// Export a singleton instance
export const cjRestApiService = new CJRestApiService();