{"name": "nextn", "version": "0.1.0", "private": true, "scripts": {"dev": "node ./node_modules/next/dist/bin/next dev -p 9003 --hostname 0.0.0.0", "dev:turbo": "next dev --turbo -p 9003 --hostname 0.0.0.0", "genkit:dev": "genkit start -- tsx src/ai/dev.ts", "genkit:watch": "genkit start -- tsx --watch src/ai/dev.ts", "build": "next build", "analyze": "cross-env ANALYZE=true next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "setup:admin": "node scripts/setup-admin-user.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.835.0", "@aws-sdk/s3-request-presigner": "^3.835.0", "@emotion/is-prop-valid": "^1.3.1", "@genkit-ai/googleai": "^1.6.2", "@genkit-ai/next": "^1.6.2", "@headlessui/react": "^2.2.3", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^4.1.3", "@lexical/code": "^0.17.1", "@lexical/hashtag": "^0.17.1", "@lexical/link": "^0.17.1", "@lexical/list": "^0.17.1", "@lexical/markdown": "^0.17.1", "@lexical/overflow": "^0.17.1", "@lexical/react": "^0.17.1", "@lexical/rich-text": "^0.17.1", "@lexical/table": "^0.17.1", "@lexical/utils": "^0.17.1", "@modelcontextprotocol/server-filesystem": "^2025.3.28", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.0", "@tanstack/query-sync-storage-persister": "^5.76.1", "@tanstack/react-query": "^5.76.1", "@tanstack/react-query-persist-client": "^5.76.1", "@types/crypto-js": "^4.2.2", "@types/lodash": "^4.17.16", "@types/qrcode": "^1.5.5", "@types/react-window": "^1.8.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "embla-carousel-react": "^8.1.3", "fast-average-color": "^9.5.0", "framer-motion": "^12.19.2", "genkit": "^1.6.2", "lexical": "^0.17.1", "lodash": "^4.17.21", "lucide-react": "^0.475.0", "mime-types": "^3.0.1", "next": "^15.3.4", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "otplib": "^12.0.1", "patch-package": "^8.0.0", "puppeteer": "^24.11.1", "qrcode": "^1.5.4", "react": "^19.1.0", "react-day-picker": "^9.3.4", "react-dom": "^19.1.0", "react-hook-form": "^7.54.2", "react-window": "^1.8.11", "recharts": "^2.15.1", "sharp": "^0.34.2", "sonner": "^2.0.5", "steamgriddb": "^2.2.0", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "xmldom": "^0.6.0", "zod": "^3.24.2"}, "devDependencies": {"@next/bundle-analyzer": "^15.3.3", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/mime-types": "^3.0.1", "@types/node": "^20", "@types/puppeteer": "^7.0.4", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "babel-jest": "^30.0.0-beta.3", "cross-env": "^7.0.3", "eslint": "9.30.0", "eslint-config-next": "15.3.4", "genkit-cli": "^1.6.1", "jest": "^29.7.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5", "webpack-bundle-analyzer": "^4.10.2"}, "description": "<div align=\"center\">", "main": "analyze-review.js", "repository": {"type": "git", "url": "git+https://github.com/ZaphreBR/CriticalPixel.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/ZaphreBR/CriticalPixel/issues"}, "homepage": "https://github.com/ZaphreBR/CriticalPixel#readme"}