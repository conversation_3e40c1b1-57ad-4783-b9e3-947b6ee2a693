#!/usr/bin/env node

/**
 * Direct SteamGridDB Hero Banner Migration Script
 * Works directly with services without requiring web server
 */

const { createClient } = require('@supabase/supabase-js');
const sharp = require('sharp');
require('dotenv').config({ path: '.env.local' });

// Import fetch for Node.js
let fetch;
(async () => {
  const { default: nodeFetch } = await import('node-fetch');
  fetch = nodeFetch;
})();

// Initialize clients
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

const STEAMGRIDDB_API_KEY = process.env.STEAMGRIDDB_API_KEY;
const HERO_BUCKET = 'steamgriddb-heroes';

// SteamGridDB API functions
async function searchSteamGridDBGames(query) {
  try {
    const response = await fetch(`https://www.steamgriddb.com/api/v2/search/autocomplete/${encodeURIComponent(query)}`, {
      headers: {
        'Authorization': `Bearer ${STEAMGRIDDB_API_KEY}`
      }
    });

    if (!response.ok) {
      throw new Error(`SteamGridDB API error: ${response.status}`);
    }

    const data = await response.json();
    return data.data || [];
  } catch (error) {
    console.error('Error searching SteamGridDB games:', error);
    return [];
  }
}

async function getSteamGridDBHeroes(gameId, options = {}) {
  try {
    const params = new URLSearchParams({
      types: 'static',
      dimensions: '1920x620,3840x1240,1600x650',
      ...options
    });

    const response = await fetch(`https://www.steamgriddb.com/api/v2/heroes/game/${gameId}?${params}`, {
      headers: {
        'Authorization': `Bearer ${STEAMGRIDDB_API_KEY}`
      }
    });

    if (!response.ok) {
      throw new Error(`SteamGridDB API error: ${response.status}`);
    }

    const data = await response.json();
    return data.data || [];
  } catch (error) {
    console.error('Error fetching SteamGridDB heroes:', error);
    return [];
  }
}

// Image processing function
async function optimizeImage(buffer) {
  try {
    const optimized = await sharp(buffer)
      .resize(1920, 620, {
        fit: 'inside',
        withoutEnlargement: true
      })
      .webp({ 
        quality: 85,
        effort: 4
      })
      .toBuffer();

    return {
      buffer: optimized,
      size: optimized.length
    };
  } catch (error) {
    throw new Error(`Image optimization failed: ${error.message}`);
  }
}

// Select best hero banner
function selectBestHero(heroes) {
  if (!heroes.length) return null;

  const targetAspectRatio = 1920 / 620;

  return heroes.sort((a, b) => {
    // Priority 1: Exact dimensions
    const aExactDimensions = a.width === 1920 && a.height === 620;
    const bExactDimensions = b.width === 1920 && b.height === 620;
    
    if (aExactDimensions && !bExactDimensions) return -1;
    if (!aExactDimensions && bExactDimensions) return 1;

    // Priority 2: Score (higher is better)
    if (a.score !== b.score) return b.score - a.score;

    // Priority 3: Aspect ratio (closer to target is better)
    const aAspectRatio = a.width / a.height;
    const bAspectRatio = b.width / b.height;
    const aAspectDiff = Math.abs(aAspectRatio - targetAspectRatio);
    const bAspectDiff = Math.abs(bAspectRatio - targetAspectRatio);
    
    return aAspectDiff - bAspectDiff;
  })[0];
}

// Process single game
async function processGame(game) {
  const startTime = Date.now();
  
  try {
    console.log(`🎮 Processing: ${game.name}`);

    // Update status to processing
    await supabase.rpc('update_steamgriddb_hero_cache_status', {
      game_id: game.id,
      new_status: 'processing'
    });

    // Search for the game on SteamGridDB
    const games = await searchSteamGridDBGames(game.name);
    
    if (!games || games.length === 0) {
      throw new Error(`No games found on SteamGridDB for: ${game.name}`);
    }

    const steamGridDBGame = games[0];
    console.log(`   Found SteamGridDB game: ${steamGridDBGame.name} (ID: ${steamGridDBGame.id})`);
    
    // Fetch hero banners
    const heroes = await getSteamGridDBHeroes(steamGridDBGame.id, { limit: 5 });

    if (!heroes || heroes.length === 0) {
      throw new Error(`No hero banners found for: ${game.name}`);
    }

    // Select best hero
    const bestHero = selectBestHero(heroes);
    
    if (!bestHero) {
      throw new Error(`No suitable hero banner found for: ${game.name}`);
    }

    console.log(`   Selected hero: ${bestHero.width}x${bestHero.height} by ${bestHero.author?.name || 'Unknown'}`);

    // Download the image
    const response = await fetch(bestHero.url);
    
    if (!response.ok) {
      throw new Error(`Failed to download hero: ${response.status}`);
    }

    const imageBuffer = await response.arrayBuffer();
    const buffer = Buffer.from(imageBuffer);

    // Optimize the image
    const optimized = await optimizeImage(buffer);

    // Generate filename
    const filename = `${game.id}-${bestHero.id}-${Date.now()}.webp`;
    
    // Upload to Supabase storage
    const { data, error } = await supabase.storage
      .from(HERO_BUCKET)
      .upload(filename, optimized.buffer, {
        contentType: 'image/webp',
        cacheControl: '31536000'
      });

    if (error) {
      throw new Error(`Failed to upload to storage: ${error.message}`);
    }

    // Get public URL
    const { data: urlData } = supabase.storage
      .from(HERO_BUCKET)
      .getPublicUrl(filename);

    // Update game record
    await supabase.rpc('update_steamgriddb_hero_cache_status', {
      game_id: game.id,
      new_status: 'cached',
      cached_url: urlData.publicUrl,
      author_name: bestHero.author?.name || null,
      author_steam64: bestHero.author?.steam64 || null,
      author_avatar: bestHero.author?.avatar || null,
      hero_id: bestHero.id,
      sgdb_game_id: steamGridDBGame.id
    });

    // Log success
    await supabase.rpc('log_steamgriddb_hero_audit', {
      p_game_id: game.id,
      p_action: 'fetch_completed',
      p_steamgriddb_game_id: steamGridDBGame.id,
      p_steamgriddb_hero_id: bestHero.id,
      p_original_url: bestHero.url,
      p_supabase_url: urlData.publicUrl,
      p_author_name: bestHero.author?.name || null,
      p_author_steam64: bestHero.author?.steam64 || null,
      p_processing_time_ms: Date.now() - startTime,
      p_file_size_bytes: optimized.size,
      p_dimensions: `${bestHero.width}x${bestHero.height}`
    });

    console.log(`   ✅ Success! Hero cached: ${urlData.publicUrl}`);
    return { success: true, heroUrl: urlData.publicUrl };

  } catch (error) {
    console.log(`   ❌ Failed: ${error.message}`);
    
    // Update status to failed
    await supabase.rpc('update_steamgriddb_hero_cache_status', {
      game_id: game.id,
      new_status: 'failed'
    });

    // Log failure
    await supabase.rpc('log_steamgriddb_hero_audit', {
      p_game_id: game.id,
      p_action: 'fetch_failed',
      p_error_message: error.message,
      p_processing_time_ms: Date.now() - startTime
    });

    return { success: false, error: error.message };
  }
}

// Get games needing processing
async function getGamesNeedingProcessing(limit = 10) {
  const { data, error } = await supabase
    .from('games')
    .select('id, name, slug, steamgriddb_hero_cache_status')
    .or('steamgriddb_hero_cache_status.is.null,steamgriddb_hero_cache_status.eq.pending,steamgriddb_hero_cache_status.eq.failed')
    .order('created_at', { ascending: true })
    .limit(limit);

  if (error) {
    throw error;
  }

  return data || [];
}

// Main migration function
async function runMigration() {
  console.log('🚀 Starting direct SteamGridDB hero banner migration...\n');

  if (!STEAMGRIDDB_API_KEY) {
    console.error('❌ STEAMGRIDDB_API_KEY not found in environment variables');
    process.exit(1);
  }

  try {
    let totalProcessed = 0;
    let totalSuccessful = 0;
    let totalFailed = 0;

    while (true) {
      // Get next batch of games
      const games = await getGamesNeedingProcessing(5);
      
      if (games.length === 0) {
        console.log('🎉 No more games to process!');
        break;
      }

      console.log(`📦 Processing batch of ${games.length} games...\n`);

      // Process games one by one to avoid overwhelming the API
      for (const game of games) {
        const result = await processGame(game);
        
        totalProcessed++;
        if (result.success) {
          totalSuccessful++;
        } else {
          totalFailed++;
        }

        // Small delay between games
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      console.log(`\n📊 Batch complete: ${totalSuccessful}/${totalProcessed} successful\n`);

      // Delay between batches
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    console.log(`\n🎉 Migration complete!`);
    console.log(`📊 Final results: ${totalSuccessful} successful, ${totalFailed} failed out of ${totalProcessed} total\n`);

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
if (require.main === module) {
  runMigration();
}
