import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase/server';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const regionCode = searchParams.get('region') || 'br';
    const daysBackParam = searchParams.get('days');
    const daysBack = daysBackParam ? parseInt(daysBackParam, 10) : 90;
    
    // Validate game ID
    if (!params.id) {
      return NextResponse.json(
        { success: false, error: 'Game ID is required' },
        { status: 400 }
      );
    }

    // Validate days back parameter
    if (isNaN(daysBack) || daysBack < 1 || daysBack > 365) {
      return NextResponse.json(
        { success: false, error: 'Days back must be between 1 and 365' },
        { status: 400 }
      );
    }

    // Validate region code
    const validRegions = [
      'br', 'us', 'eu', 'de', 'gb', 'jp', 'ca', 'au', 'mx', 'ar', 'cl', 'co', 'pe', 'uy',
      'kr', 'cn', 'in', 'ru', 'pl', 'th', 'sg', 'hk', 'tw', 'nz', 'no', 'se', 'dk', 'fi',
      'ch', 'tr', 'ua'
    ];
    
    if (!validRegions.includes(regionCode)) {
      return NextResponse.json(
        { success: false, error: 'Invalid region code' },
        { status: 400 }
      );
    }

    console.log(`🔍 API: Fetching Steam price history for game ${params.id} in region ${regionCode} (${daysBack} days)`);

    const supabase = await createServerClient();
    
    // Check if price history exists
    const { data: existingData } = await supabase
      .from('steam_price_history')
      .select('id')
      .eq('game_id', params.id)
      .eq('region_code', regionCode)
      .limit(1);
    
    if (!existingData || existingData.length === 0) {
      console.log(`📊 No historical Steam price data found for game ${params.id} in region ${regionCode}`);
      console.log(`📈 Price history will start collecting when Steam prices are next updated`);
    }

    // Get price history using the database function
    const { data: historyData, error: historyError } = await supabase
      .rpc('get_steam_price_history_for_chart', {
        p_game_id: params.id,
        p_region_code: regionCode,
        p_days_back: daysBack
      });

    if (historyError) {
      console.error('Error fetching Steam price history:', historyError);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch price history data' },
        { status: 500 }
      );
    }

    if (!historyData || historyData.length === 0) {
      return NextResponse.json({
        success: true,
        data: {
          gameId: params.id,
          regionCode,
          history: []
        }
      });
    }

    // Get lowest price ever
    const { data: lowestPriceData } = await supabase
      .rpc('get_lowest_steam_price', {
        p_game_id: params.id,
        p_region_code: regionCode
      });

    // Get current price from game_prices table
    const { data: currentPriceData } = await supabase
      .from('game_prices')
      .select('price, currency')
      .eq('game_id', params.id)
      .eq('store_name', 'Steam')
      .eq('region_code', regionCode)
      .single();

    // Convert current price string to cents for comparison
    let currentPriceCents: number | undefined;
    if (currentPriceData?.price) {
      currentPriceCents = convertPriceStringToCents(
        currentPriceData.price, 
        currentPriceData.currency
      );
    }

    const priceHistoryData = {
      gameId: params.id,
      regionCode,
      history: historyData.map((point: any) => ({
        id: `${params.id}-${regionCode}-${point.recorded_at}`,
        price_cents: point.price_cents,
        currency: point.currency,
        recorded_at: point.recorded_at,
        discount_percentage: point.discount_percentage || 0,
        original_price_cents: point.original_price_cents || null,
        price_type: point.price_type || 'regular'
      })),
      lowestPrice: lowestPriceData?.[0] ? {
        price_cents: lowestPriceData[0].lowest_price_cents,
        currency: lowestPriceData[0].currency,
        recorded_at: lowestPriceData[0].recorded_at
      } : undefined,
      currentPrice: currentPriceCents ? {
        price_cents: currentPriceCents,
        currency: currentPriceData.currency
      } : undefined
    };

    // Calculate basic statistics
    const prices = historyData.map((h: any) => h.price_cents);
    const statistics = prices.length > 0 ? {
      lowest: Math.min(...prices),
      highest: Math.max(...prices),
      average: Math.round(prices.reduce((sum: number, price: number) => sum + price, 0) / prices.length),
      current: currentPriceCents,
      currency: historyData[0]?.currency || 'BRL',
      totalDataPoints: prices.length
    } : null;

    // Calculate additional metrics
    const priceChangeFromLowest = priceHistoryData.lowestPrice && priceHistoryData.currentPrice ?
      Math.round(((priceHistoryData.currentPrice.price_cents - priceHistoryData.lowestPrice.price_cents) / priceHistoryData.lowestPrice.price_cents) * 100) : null;
    
    const response = {
      success: true,
      data: {
        ...priceHistoryData,
        statistics,
        metadata: {
          requestedRegion: regionCode,
          requestedDaysBack: daysBack,
          dataPoints: priceHistoryData.history.length,
          timeRange: priceHistoryData.history.length > 0 ? {
            start: priceHistoryData.history[0]?.recorded_at,
            end: priceHistoryData.history[priceHistoryData.history.length - 1]?.recorded_at
          } : null,
          priceChange: priceChangeFromLowest
        }
      }
    };

    console.log(`✅ API: Successfully fetched Steam price history with ${priceHistoryData.history.length} data points`);

    return NextResponse.json(response, {
      headers: {
        'Cache-Control': 'public, s-maxage=3600, stale-while-revalidate=7200', // Cache for 1 hour
      },
    });

  } catch (error) {
    console.error('Error in Steam price history API:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error while fetching price history',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

// Helper function to convert price string to cents
function convertPriceStringToCents(priceString: string, currency: string): number {
  try {
    let cleanPrice: string;
    
    switch (currency) {
      case 'BRL':
        cleanPrice = priceString.replace('R$ ', '').replace(',', '.');
        break;
      case 'USD':
        cleanPrice = priceString.replace('$', '');
        break;
      case 'EUR':
        cleanPrice = priceString.replace('€', '');
        break;
      default:
        cleanPrice = priceString.replace(/[^0-9.]/g, '');
    }
    
    return Math.round(parseFloat(cleanPrice) * 100);
  } catch (error) {
    console.error('Error converting price string to cents:', error);
    return 0;
  }
}

// Price history will now ONLY collect real Steam price data
// No sample data generation - only real historical Steam prices