-- SteamGridDB Heroes Storage Migration
-- Date: February 2, 2025
-- Purpose: Add support for caching SteamGridDB hero banners (1920x620) in Supabase storage

-- Add new columns to games table for SteamGridDB hero banner caching
ALTER TABLE games ADD COLUMN IF NOT EXISTS steamgriddb_hero_url TEXT;
ALTER TABLE games ADD COLUMN IF NOT EXISTS steamgriddb_hero_cached_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE games ADD COLUMN IF NOT EXISTS steamgriddb_hero_cache_status TEXT DEFAULT 'pending' CHECK (steamgriddb_hero_cache_status IN ('pending', 'processing', 'cached', 'failed'));
ALTER TABLE games ADD COLUMN IF NOT EXISTS steamgriddb_hero_author_name TEXT;
ALTER TABLE games ADD COLUMN IF NOT EXISTS steamgriddb_hero_author_steam64 TEXT;
ALTER TABLE games ADD COLUMN IF NOT EXISTS steamgriddb_hero_author_avatar TEXT;
ALTER TABLE games ADD COLUMN IF NOT EXISTS steamgriddb_hero_id INTEGER;
ALTER TABLE games ADD COLUMN IF NOT EXISTS steamgriddb_game_id INTEGER;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_games_hero_cache_status ON games(steamgriddb_hero_cache_status);
CREATE INDEX IF NOT EXISTS idx_games_hero_cached_at ON games(steamgriddb_hero_cached_at DESC);
CREATE INDEX IF NOT EXISTS idx_games_steamgriddb_game_id ON games(steamgriddb_game_id);

-- Create storage bucket for SteamGridDB hero banners
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'steamgriddb-heroes',
  'steamgriddb-heroes',
  true,
  10485760, -- 10MB limit (SteamGridDB allows up to 75MB, but we'll compress)
  ARRAY['image/jpeg', 'image/png', 'image/webp']
) ON CONFLICT (id) DO NOTHING;

-- Storage policies for SteamGridDB heroes bucket
-- Policy 1: Allow public read access to hero banners
CREATE POLICY "Public read access for SteamGridDB heroes" ON storage.objects
  FOR SELECT USING (bucket_id = 'steamgriddb-heroes');

-- Policy 2: Allow service role to upload heroes
CREATE POLICY "Service role can upload SteamGridDB heroes" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'steamgriddb-heroes' AND
    auth.role() = 'service_role'
  );

-- Policy 3: Allow service role to update heroes
CREATE POLICY "Service role can update SteamGridDB heroes" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'steamgriddb-heroes' AND
    auth.role() = 'service_role'
  );

-- Policy 4: Allow service role to delete heroes
CREATE POLICY "Service role can delete SteamGridDB heroes" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'steamgriddb-heroes' AND
    auth.role() = 'service_role'
  );

-- Function to get optimal hero banner URL (prioritize SteamGridDB, fallback to cover)
CREATE OR REPLACE FUNCTION get_optimal_hero_url(game_row games)
RETURNS TEXT AS $$
BEGIN
  -- Priority 1: Cached SteamGridDB hero banner
  IF game_row.steamgriddb_hero_cache_status = 'cached' AND game_row.steamgriddb_hero_url IS NOT NULL THEN
    RETURN game_row.steamgriddb_hero_url;
  END IF;
  
  -- Priority 2: Cached Supabase cover (as fallback)
  IF game_row.cover_cache_status = 'cached' AND game_row.supabase_cover_url IS NOT NULL THEN
    RETURN game_row.supabase_cover_url;
  END IF;
  
  -- Priority 3: Original IGDB cover URL
  RETURN game_row.cover_url;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to update SteamGridDB hero cache status
CREATE OR REPLACE FUNCTION update_steamgriddb_hero_cache_status(
  game_id UUID,
  new_status TEXT,
  cached_url TEXT DEFAULT NULL,
  author_name TEXT DEFAULT NULL,
  author_steam64 TEXT DEFAULT NULL,
  author_avatar TEXT DEFAULT NULL,
  hero_id INTEGER DEFAULT NULL,
  sgdb_game_id INTEGER DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
  UPDATE games 
  SET 
    steamgriddb_hero_cache_status = new_status,
    steamgriddb_hero_url = COALESCE(cached_url, steamgriddb_hero_url),
    steamgriddb_hero_author_name = COALESCE(author_name, steamgriddb_hero_author_name),
    steamgriddb_hero_author_steam64 = COALESCE(author_steam64, steamgriddb_hero_author_steam64),
    steamgriddb_hero_author_avatar = COALESCE(author_avatar, steamgriddb_hero_author_avatar),
    steamgriddb_hero_id = COALESCE(hero_id, steamgriddb_hero_id),
    steamgriddb_game_id = COALESCE(sgdb_game_id, steamgriddb_game_id),
    steamgriddb_hero_cached_at = CASE 
      WHEN new_status = 'cached' THEN NOW()
      ELSE steamgriddb_hero_cached_at
    END
  WHERE id = game_id;
END;
$$ LANGUAGE plpgsql;

-- Create audit table for SteamGridDB hero processing
CREATE TABLE IF NOT EXISTS steamgriddb_hero_audit (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  game_id UUID NOT NULL REFERENCES games(id) ON DELETE CASCADE,
  action TEXT NOT NULL CHECK (action IN ('fetch_started', 'fetch_completed', 'fetch_failed', 'cache_deleted')),
  steamgriddb_game_id INTEGER,
  steamgriddb_hero_id INTEGER,
  original_hero_url TEXT,
  supabase_hero_url TEXT,
  author_name TEXT,
  author_steam64 TEXT,
  error_message TEXT,
  processing_time_ms INTEGER,
  file_size_bytes INTEGER,
  dimensions TEXT, -- e.g., "1920x620"
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for audit table
CREATE INDEX IF NOT EXISTS idx_steamgriddb_hero_audit_game_id ON steamgriddb_hero_audit(game_id);
CREATE INDEX IF NOT EXISTS idx_steamgriddb_hero_audit_created_at ON steamgriddb_hero_audit(created_at DESC);

-- Function to log SteamGridDB hero audit events
CREATE OR REPLACE FUNCTION log_steamgriddb_hero_audit(
  p_game_id UUID,
  p_action TEXT,
  p_steamgriddb_game_id INTEGER DEFAULT NULL,
  p_steamgriddb_hero_id INTEGER DEFAULT NULL,
  p_original_url TEXT DEFAULT NULL,
  p_supabase_url TEXT DEFAULT NULL,
  p_author_name TEXT DEFAULT NULL,
  p_author_steam64 TEXT DEFAULT NULL,
  p_error_message TEXT DEFAULT NULL,
  p_processing_time_ms INTEGER DEFAULT NULL,
  p_file_size_bytes INTEGER DEFAULT NULL,
  p_dimensions TEXT DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
  INSERT INTO steamgriddb_hero_audit (
    game_id,
    action,
    steamgriddb_game_id,
    steamgriddb_hero_id,
    original_hero_url,
    supabase_hero_url,
    author_name,
    author_steam64,
    error_message,
    processing_time_ms,
    file_size_bytes,
    dimensions
  ) VALUES (
    p_game_id,
    p_action,
    p_steamgriddb_game_id,
    p_steamgriddb_hero_id,
    p_original_url,
    p_supabase_url,
    p_author_name,
    p_author_steam64,
    p_error_message,
    p_processing_time_ms,
    p_file_size_bytes,
    p_dimensions
  );
END;
$$ LANGUAGE plpgsql;

-- Create view for games with optimal hero URLs
CREATE OR REPLACE VIEW games_with_heroes AS
SELECT 
  g.*,
  get_optimal_hero_url(g) as optimal_hero_url,
  CASE 
    WHEN g.steamgriddb_hero_cache_status = 'cached' AND g.steamgriddb_hero_url IS NOT NULL 
    THEN 'steamgriddb'
    WHEN g.cover_cache_status = 'cached' AND g.supabase_cover_url IS NOT NULL 
    THEN 'supabase_cover'
    ELSE 'igdb_cover'
  END as hero_source,
  -- Credit information for SteamGridDB heroes
  CASE 
    WHEN g.steamgriddb_hero_cache_status = 'cached' AND g.steamgriddb_hero_url IS NOT NULL 
    THEN jsonb_build_object(
      'source', 'SteamGridDB',
      'author_name', g.steamgriddb_hero_author_name,
      'author_steam64', g.steamgriddb_hero_author_steam64,
      'author_avatar', g.steamgriddb_hero_author_avatar,
      'hero_id', g.steamgriddb_hero_id,
      'game_id', g.steamgriddb_game_id
    )
    ELSE NULL
  END as hero_credits
FROM games g;

-- Add comment to document the migration
COMMENT ON TABLE steamgriddb_hero_audit IS 'Audit log for SteamGridDB hero banner processing and caching operations';
COMMENT ON COLUMN games.steamgriddb_hero_url IS 'URL to cached SteamGridDB hero banner (1920x620) in Supabase storage';
COMMENT ON COLUMN games.steamgriddb_hero_author_name IS 'Name of the SteamGridDB user who created the hero banner';

-- Verify migration
DO $$
BEGIN
    -- Check if new columns exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'games' AND column_name = 'steamgriddb_hero_url'
    ) THEN
        RAISE EXCEPTION 'steamgriddb_hero_url column was not created';
    END IF;
    
    -- Check if bucket exists
    IF NOT EXISTS (
        SELECT 1 FROM storage.buckets WHERE id = 'steamgriddb-heroes'
    ) THEN
        RAISE EXCEPTION 'steamgriddb-heroes bucket was not created';
    END IF;
    
    RAISE NOTICE 'SteamGridDB heroes storage migration completed successfully!';
END
$$;
