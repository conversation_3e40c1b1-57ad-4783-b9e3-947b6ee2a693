/**
 * React hook for managing SteamGridDB hero banners
 */

import { useState, useEffect, useCallback } from 'react';

export interface HeroCredits {
  source: string;
  authorName: string;
  authorSteam64?: string;
  authorAvatar?: string;
  heroId: number;
  gameId: number;
}

export interface HeroInfo {
  heroUrl?: string;
  credits?: HeroCredits;
  status: 'pending' | 'processing' | 'cached' | 'failed';
}

export interface UseSteamGridDBHeroResult {
  heroInfo: HeroInfo | null;
  loading: boolean;
  error: string | null;
  fetchHero: (gameId: string, gameName: string) => Promise<void>;
  getHeroInfo: (gameId: string) => Promise<void>;
  clearError: () => void;
}

export function useSteamGridDBHero(): UseSteamGridDBHeroResult {
  const [heroInfo, setHeroInfo] = useState<HeroInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const getHeroInfo = useCallback(async (gameId: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/steamgriddb/fetch-hero?gameId=${gameId}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success) {
        setHeroInfo(data.data);
      } else {
        throw new Error(data.error || 'Failed to get hero info');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('Failed to get hero info:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchHero = useCallback(async (gameId: string, gameName: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/steamgriddb/fetch-hero', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ gameId, gameName }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success) {
        setHeroInfo(data.data);
      } else {
        throw new Error(data.error || 'Failed to fetch hero banner');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('Failed to fetch hero banner:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    heroInfo,
    loading,
    error,
    fetchHero,
    getHeroInfo,
    clearError
  };
}

/**
 * Hook for automatically fetching hero info on mount
 */
export function useSteamGridDBHeroAuto(gameId: string): UseSteamGridDBHeroResult {
  const hook = useSteamGridDBHero();

  useEffect(() => {
    if (gameId) {
      hook.getHeroInfo(gameId);
    }
  }, [gameId, hook.getHeroInfo]);

  return hook;
}

/**
 * Hook for triggering hero fetch with automatic retry
 */
export function useSteamGridDBHeroWithFetch(
  gameId: string, 
  gameName: string,
  autoFetch: boolean = false
): UseSteamGridDBHeroResult & { triggerFetch: () => void } {
  const hook = useSteamGridDBHero();

  const triggerFetch = useCallback(() => {
    if (gameId && gameName) {
      hook.fetchHero(gameId, gameName);
    }
  }, [gameId, gameName, hook.fetchHero]);

  useEffect(() => {
    if (gameId) {
      // First, get current hero info
      hook.getHeroInfo(gameId).then(() => {
        // If autoFetch is enabled and no hero is cached, trigger fetch
        if (autoFetch && hook.heroInfo?.status === 'pending') {
          triggerFetch();
        }
      });
    }
  }, [gameId, autoFetch, hook.getHeroInfo, triggerFetch]);

  return {
    ...hook,
    triggerFetch
  };
}
