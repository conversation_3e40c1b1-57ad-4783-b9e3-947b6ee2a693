/**
 * Hero Migration Service
 * Handles backwards compatibility and bulk processing of existing games
 */

import { createClient } from '@supabase/supabase-js';
import { SteamGridDBHeroService } from './steamGridDBHeroService';

// Supabase client for server-side operations
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export interface MigrationStats {
  totalGames: number;
  processed: number;
  successful: number;
  failed: number;
  skipped: number;
  inProgress: number;
}

export interface MigrationOptions {
  batchSize?: number;
  delayBetweenBatches?: number;
  maxConcurrent?: number;
  skipExisting?: boolean;
  gameIds?: string[];
}

export class HeroMigrationService {
  private static readonly DEFAULT_BATCH_SIZE = 10;
  private static readonly DEFAULT_DELAY = 2000; // 2 seconds between batches
  private static readonly DEFAULT_MAX_CONCURRENT = 3;

  /**
   * Get migration statistics
   */
  static async getMigrationStats(): Promise<MigrationStats> {
    try {
      // Get total games count
      const { count: totalGames } = await supabase
        .from('games')
        .select('*', { count: 'exact', head: true });

      // Get games by hero cache status
      const { data: statusCounts } = await supabase
        .from('games')
        .select('steamgriddb_hero_cache_status')
        .not('steamgriddb_hero_cache_status', 'is', null);

      const stats = {
        totalGames: totalGames || 0,
        processed: 0,
        successful: 0,
        failed: 0,
        skipped: 0,
        inProgress: 0
      };

      if (statusCounts) {
        statusCounts.forEach(game => {
          const status = game.steamgriddb_hero_cache_status;
          stats.processed++;
          
          switch (status) {
            case 'cached':
              stats.successful++;
              break;
            case 'failed':
              stats.failed++;
              break;
            case 'processing':
              stats.inProgress++;
              break;
            default:
              stats.skipped++;
          }
        });
      }

      return stats;
    } catch (error) {
      console.error('Failed to get migration stats:', error);
      return {
        totalGames: 0,
        processed: 0,
        successful: 0,
        failed: 0,
        skipped: 0,
        inProgress: 0
      };
    }
  }

  /**
   * Get games that need hero processing
   */
  static async getGamesNeedingProcessing(
    limit: number = 100,
    offset: number = 0
  ): Promise<Array<{ id: string; name: string; slug: string }>> {
    try {
      const { data, error } = await supabase
        .from('games')
        .select('id, name, slug, steamgriddb_hero_cache_status')
        .or('steamgriddb_hero_cache_status.is.null,steamgriddb_hero_cache_status.eq.pending,steamgriddb_hero_cache_status.eq.failed')
        .order('created_at', { ascending: true })
        .range(offset, offset + limit - 1);

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Failed to get games needing processing:', error);
      return [];
    }
  }

  /**
   * Process a batch of games for hero banners
   */
  static async processBatch(
    games: Array<{ id: string; name: string; slug: string }>,
    options: { maxConcurrent?: number } = {}
  ): Promise<{
    successful: string[];
    failed: Array<{ id: string; error: string }>;
  }> {
    const maxConcurrent = options.maxConcurrent || this.DEFAULT_MAX_CONCURRENT;
    const successful: string[] = [];
    const failed: Array<{ id: string; error: string }> = [];

    // Process games in smaller concurrent batches
    for (let i = 0; i < games.length; i += maxConcurrent) {
      const batch = games.slice(i, i + maxConcurrent);
      
      const promises = batch.map(async (game) => {
        try {
          console.log(`Processing hero banner for game: ${game.name} (${game.id})`);
          
          const result = await SteamGridDBHeroService.fetchAndCacheHeroBanner(
            game.id,
            game.name
          );

          if (result.success) {
            successful.push(game.id);
            console.log(`✅ Successfully processed hero for: ${game.name}`);
          } else {
            failed.push({ id: game.id, error: result.error || 'Unknown error' });
            console.log(`❌ Failed to process hero for: ${game.name} - ${result.error}`);
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          failed.push({ id: game.id, error: errorMessage });
          console.log(`❌ Exception processing hero for: ${game.name} - ${errorMessage}`);
        }
      });

      // Wait for current batch to complete
      await Promise.allSettled(promises);
      
      // Small delay between batches to avoid overwhelming the API
      if (i + maxConcurrent < games.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    return { successful, failed };
  }

  /**
   * Run full migration for all games needing processing
   */
  static async runMigration(options: MigrationOptions = {}): Promise<MigrationStats> {
    const {
      batchSize = this.DEFAULT_BATCH_SIZE,
      delayBetweenBatches = this.DEFAULT_DELAY,
      maxConcurrent = this.DEFAULT_MAX_CONCURRENT,
      skipExisting = true,
      gameIds
    } = options;

    console.log('🚀 Starting SteamGridDB hero banner migration...');
    
    const startTime = Date.now();
    let totalProcessed = 0;
    let totalSuccessful = 0;
    let totalFailed = 0;
    let offset = 0;

    try {
      while (true) {
        // Get next batch of games
        let games: Array<{ id: string; name: string; slug: string }>;
        
        if (gameIds && gameIds.length > 0) {
          // Process specific games
          const { data } = await supabase
            .from('games')
            .select('id, name, slug')
            .in('id', gameIds);
          
          games = data || [];
          if (games.length === 0) break;
        } else {
          // Get games needing processing
          games = await this.getGamesNeedingProcessing(batchSize, offset);
          if (games.length === 0) break;
        }

        console.log(`📦 Processing batch of ${games.length} games (offset: ${offset})`);

        // Process the batch
        const result = await this.processBatch(games, { maxConcurrent });
        
        totalProcessed += games.length;
        totalSuccessful += result.successful.length;
        totalFailed += result.failed.length;

        console.log(`✅ Batch complete: ${result.successful.length} successful, ${result.failed.length} failed`);

        // If processing specific games, break after first batch
        if (gameIds && gameIds.length > 0) break;

        offset += batchSize;

        // Delay between batches
        if (delayBetweenBatches > 0) {
          console.log(`⏳ Waiting ${delayBetweenBatches}ms before next batch...`);
          await new Promise(resolve => setTimeout(resolve, delayBetweenBatches));
        }
      }

      const duration = Date.now() - startTime;
      console.log(`🎉 Migration complete! Processed ${totalProcessed} games in ${duration}ms`);
      console.log(`📊 Results: ${totalSuccessful} successful, ${totalFailed} failed`);

      return await this.getMigrationStats();

    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }
  }

  /**
   * Process a single game by ID
   */
  static async processSingleGame(gameId: string): Promise<{
    success: boolean;
    error?: string;
    heroUrl?: string;
  }> {
    try {
      // Get game info
      const { data: game, error } = await supabase
        .from('games')
        .select('id, name, slug')
        .eq('id', gameId)
        .single();

      if (error || !game) {
        return { success: false, error: 'Game not found' };
      }

      // Process the game
      const result = await SteamGridDBHeroService.fetchAndCacheHeroBanner(
        game.id,
        game.name
      );

      return {
        success: result.success,
        error: result.error,
        heroUrl: result.heroUrl
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Reset hero cache status for games (useful for re-processing)
   */
  static async resetHeroCacheStatus(gameIds?: string[]): Promise<number> {
    try {
      let query = supabase
        .from('games')
        .update({
          steamgriddb_hero_cache_status: 'pending',
          steamgriddb_hero_url: null,
          steamgriddb_hero_cached_at: null,
          steamgriddb_hero_author_name: null,
          steamgriddb_hero_author_steam64: null,
          steamgriddb_hero_author_avatar: null,
          steamgriddb_hero_id: null,
          steamgriddb_game_id: null
        });

      if (gameIds && gameIds.length > 0) {
        query = query.in('id', gameIds);
      }

      const { count, error } = await query;

      if (error) {
        throw error;
      }

      return count || 0;
    } catch (error) {
      console.error('Failed to reset hero cache status:', error);
      return 0;
    }
  }

  /**
   * Check if a game needs hero processing
   */
  static async gameNeedsProcessing(gameId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('games')
        .select('steamgriddb_hero_cache_status')
        .eq('id', gameId)
        .single();

      if (error || !data) {
        return true; // Default to needing processing
      }

      const status = data.steamgriddb_hero_cache_status;
      return !status || status === 'pending' || status === 'failed';
    } catch (error) {
      return true; // Default to needing processing on error
    }
  }
}
