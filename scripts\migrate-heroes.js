#!/usr/bin/env node

/**
 * CLI Script for SteamGridDB Hero Banner Migration
 * 
 * Usage:
 *   node scripts/migrate-heroes.js --help
 *   node scripts/migrate-heroes.js --stats
 *   node scripts/migrate-heroes.js --migrate
 *   node scripts/migrate-heroes.js --migrate --batch-size=5 --max-concurrent=2
 *   node scripts/migrate-heroes.js --process-game=<game-id>
 *   node scripts/migrate-heroes.js --reset
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Parse command line arguments
const args = process.argv.slice(2);
const options = {};

args.forEach(arg => {
  if (arg.startsWith('--')) {
    const [key, value] = arg.slice(2).split('=');
    options[key] = value || true;
  }
});

// Helper functions
async function getStats() {
  try {
    console.log('📊 Getting migration statistics...\n');

    // Get total games count
    const { count: totalGames } = await supabase
      .from('games')
      .select('*', { count: 'exact', head: true });

    // Get games by hero cache status
    const { data: statusCounts } = await supabase
      .from('games')
      .select('steamgriddb_hero_cache_status')
      .not('steamgriddb_hero_cache_status', 'is', null);

    const stats = {
      totalGames: totalGames || 0,
      processed: 0,
      successful: 0,
      failed: 0,
      skipped: 0,
      inProgress: 0
    };

    if (statusCounts) {
      statusCounts.forEach(game => {
        const status = game.steamgriddb_hero_cache_status;
        stats.processed++;
        
        switch (status) {
          case 'cached':
            stats.successful++;
            break;
          case 'failed':
            stats.failed++;
            break;
          case 'processing':
            stats.inProgress++;
            break;
          default:
            stats.skipped++;
        }
      });
    }

    console.log('📈 Migration Statistics:');
    console.log(`   Total Games: ${stats.totalGames.toLocaleString()}`);
    console.log(`   Processed: ${stats.processed.toLocaleString()}`);
    console.log(`   Successful: ${stats.successful.toLocaleString()}`);
    console.log(`   Failed: ${stats.failed.toLocaleString()}`);
    console.log(`   In Progress: ${stats.inProgress.toLocaleString()}`);
    console.log(`   Remaining: ${(stats.totalGames - stats.processed).toLocaleString()}`);
    
    const percentage = stats.totalGames > 0 ? ((stats.processed / stats.totalGames) * 100).toFixed(1) : 0;
    console.log(`   Progress: ${percentage}%\n`);

    return stats;
  } catch (error) {
    console.error('❌ Failed to get stats:', error.message);
    process.exit(1);
  }
}

async function startMigration() {
  const batchSize = parseInt(options['batch-size']) || 10;
  const maxConcurrent = parseInt(options['max-concurrent']) || 3;
  const delayBetweenBatches = parseInt(options['delay']) || 2000;

  console.log('🚀 Starting SteamGridDB hero banner migration...');
  console.log(`   Batch Size: ${batchSize}`);
  console.log(`   Max Concurrent: ${maxConcurrent}`);
  console.log(`   Delay Between Batches: ${delayBetweenBatches}ms\n`);

  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/admin/migrate-heroes`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'migrate',
        batchSize,
        maxConcurrent,
        delayBetweenBatches,
        skipExisting: true
      }),
    });

    const data = await response.json();
    
    if (data.success) {
      console.log('✅ Migration started successfully!');
      console.log('   Check the application logs for progress updates.');
      console.log('   Use --stats to monitor progress.\n');
    } else {
      throw new Error(data.error || 'Failed to start migration');
    }
  } catch (error) {
    console.error('❌ Failed to start migration:', error.message);
    process.exit(1);
  }
}

async function processGame(gameId) {
  console.log(`🎮 Processing single game: ${gameId}...\n`);

  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/admin/migrate-heroes`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'process-single',
        gameId
      }),
    });

    const data = await response.json();
    
    if (data.success) {
      console.log('✅ Game processed successfully!');
      if (data.data.heroUrl) {
        console.log(`   Hero URL: ${data.data.heroUrl}`);
      }
    } else {
      console.log(`❌ Processing failed: ${data.message}`);
    }
  } catch (error) {
    console.error('❌ Failed to process game:', error.message);
    process.exit(1);
  }
}

async function resetCache() {
  console.log('⚠️  Resetting all hero cache statuses...\n');

  if (!options.force) {
    console.log('❌ This action requires --force flag to confirm.');
    console.log('   Usage: node scripts/migrate-heroes.js --reset --force\n');
    process.exit(1);
  }

  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/admin/migrate-heroes`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'reset'
      }),
    });

    const data = await response.json();
    
    if (data.success) {
      console.log(`✅ Reset complete! ${data.data.resetCount} games marked for re-processing.\n`);
    } else {
      throw new Error(data.error || 'Failed to reset cache');
    }
  } catch (error) {
    console.error('❌ Failed to reset cache:', error.message);
    process.exit(1);
  }
}

function showHelp() {
  console.log(`
🎮 SteamGridDB Hero Banner Migration CLI

Usage:
  node scripts/migrate-heroes.js [options]

Options:
  --help                    Show this help message
  --stats                   Show migration statistics
  --migrate                 Start full migration
  --batch-size=N           Set batch size (default: 10)
  --max-concurrent=N       Set max concurrent processing (default: 3)
  --delay=N                Set delay between batches in ms (default: 2000)
  --process-game=ID        Process a single game by ID
  --reset --force          Reset all hero cache statuses (requires --force)

Examples:
  node scripts/migrate-heroes.js --stats
  node scripts/migrate-heroes.js --migrate
  node scripts/migrate-heroes.js --migrate --batch-size=5 --max-concurrent=2
  node scripts/migrate-heroes.js --process-game=123e4567-e89b-12d3-a456-426614174000
  node scripts/migrate-heroes.js --reset --force

Environment:
  Make sure .env.local contains:
  - NEXT_PUBLIC_SUPABASE_URL
  - SUPABASE_SERVICE_ROLE_KEY
  - STEAMGRIDDB_API_KEY
`);
}

// Main execution
async function main() {
  if (options.help) {
    showHelp();
    return;
  }

  if (options.stats) {
    await getStats();
    return;
  }

  if (options.migrate) {
    await startMigration();
    return;
  }

  if (options['process-game']) {
    await processGame(options['process-game']);
    return;
  }

  if (options.reset) {
    await resetCache();
    return;
  }

  // Default to showing help
  showHelp();
}

// Run the script
main().catch(error => {
  console.error('❌ Script failed:', error.message);
  process.exit(1);
});
