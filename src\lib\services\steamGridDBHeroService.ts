/**
 * SteamGridDB Hero Banner Service
 * Handles fetching, processing, and caching of SteamGridDB hero banners (1920x620)
 */

import { createClient } from '@supabase/supabase-js';
import { searchSteamGridDBGames, getSteamGridDBHeroes } from '@/lib/steamgriddb-api';
import { ImageOptimizer } from '@/lib/performance/imageOptimization';

// Supabase client for server-side operations
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export interface SteamGridDBHero {
  id: number;
  url: string;
  thumb: string;
  tags: string[];
  style: string;
  width: number;
  height: number;
  score: number;
  author: {
    name: string;
    steam64: string | null;
    avatar: string | null;
  };
  notes: string | null;
}

export interface HeroBannerResult {
  success: boolean;
  heroUrl?: string;
  credits?: {
    source: string;
    authorName: string;
    authorSteam64?: string;
    authorAvatar?: string;
    heroId: number;
    gameId: number;
  };
  error?: string;
}

export class SteamGridDBHeroService {
  private static readonly HERO_BUCKET = 'steamgriddb-heroes';
  private static readonly TARGET_WIDTH = 1920;
  private static readonly TARGET_HEIGHT = 620;
  private static readonly MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

  /**
   * Fetch and cache SteamGridDB hero banner for a game
   */
  static async fetchAndCacheHeroBanner(
    gameId: string,
    gameName: string
  ): Promise<HeroBannerResult> {
    const startTime = Date.now();
    
    try {
      // Log start of processing
      await this.logAudit(gameId, 'fetch_started', null, null, null, null, null, null, null, null, null, null);

      // Update status to processing
      await this.updateHeroCacheStatus(gameId, 'processing');

      // Search for the game on SteamGridDB
      const games = await searchSteamGridDBGames(gameName);
      
      if (!games || games.length === 0) {
        const error = `No games found on SteamGridDB for: ${gameName}`;
        await this.updateHeroCacheStatus(gameId, 'failed');
        await this.logAudit(gameId, 'fetch_failed', null, null, null, null, null, null, error, Date.now() - startTime, null, null);
        return { success: false, error };
      }

      // Use the first (most relevant) game result
      const steamGridDBGame = games[0];
      
      // Fetch hero banners for the game
      const heroes = await getSteamGridDBHeroes(steamGridDBGame.id, {
        limit: 5 // Get top 5 heroes to choose from
      });

      if (!heroes || heroes.length === 0) {
        const error = `No hero banners found for game: ${gameName}`;
        await this.updateHeroCacheStatus(gameId, 'failed');
        await this.logAudit(gameId, 'fetch_failed', steamGridDBGame.id, null, null, null, null, null, error, Date.now() - startTime, null, null);
        return { success: false, error };
      }

      // Find the best hero banner (prefer 1920x620, highest score)
      const bestHero = this.selectBestHero(heroes);
      
      if (!bestHero) {
        const error = `No suitable hero banner found for: ${gameName}`;
        await this.updateHeroCacheStatus(gameId, 'failed');
        await this.logAudit(gameId, 'fetch_failed', steamGridDBGame.id, null, null, null, null, null, error, Date.now() - startTime, null, null);
        return { success: false, error };
      }

      // Download and process the hero banner
      const processedResult = await this.downloadAndProcessHero(bestHero, gameId, gameName);
      
      if (!processedResult.success) {
        await this.updateHeroCacheStatus(gameId, 'failed');
        await this.logAudit(gameId, 'fetch_failed', steamGridDBGame.id, bestHero.id, bestHero.url, null, bestHero.author.name, bestHero.author.steam64, processedResult.error, Date.now() - startTime, null, `${bestHero.width}x${bestHero.height}`);
        return processedResult;
      }

      // Update game record with cached hero information
      await this.updateHeroCacheStatus(
        gameId,
        'cached',
        processedResult.heroUrl,
        bestHero.author.name,
        bestHero.author.steam64,
        bestHero.author.avatar,
        bestHero.id,
        steamGridDBGame.id
      );

      // Log successful completion
      await this.logAudit(
        gameId,
        'fetch_completed',
        steamGridDBGame.id,
        bestHero.id,
        bestHero.url,
        processedResult.heroUrl,
        bestHero.author.name,
        bestHero.author.steam64,
        null,
        Date.now() - startTime,
        processedResult.fileSize,
        `${bestHero.width}x${bestHero.height}`
      );

      return {
        success: true,
        heroUrl: processedResult.heroUrl,
        credits: {
          source: 'SteamGridDB',
          authorName: bestHero.author.name,
          authorSteam64: bestHero.author.steam64 || undefined,
          authorAvatar: bestHero.author.avatar || undefined,
          heroId: bestHero.id,
          gameId: steamGridDBGame.id
        }
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      await this.updateHeroCacheStatus(gameId, 'failed');
      await this.logAudit(gameId, 'fetch_failed', null, null, null, null, null, null, errorMessage, Date.now() - startTime, null, null);
      
      return {
        success: false,
        error: `Failed to fetch SteamGridDB hero: ${errorMessage}`
      };
    }
  }

  /**
   * Select the best hero banner from available options
   */
  private static selectBestHero(heroes: SteamGridDBHero[]): SteamGridDBHero | null {
    if (!heroes.length) return null;

    // Sort heroes by preference:
    // 1. Exact 1920x620 dimensions (highest priority)
    // 2. Highest score
    // 3. Closest to target aspect ratio (1920/620 ≈ 3.097)
    const targetAspectRatio = this.TARGET_WIDTH / this.TARGET_HEIGHT;

    return heroes.sort((a, b) => {
      // Priority 1: Exact dimensions
      const aExactDimensions = a.width === this.TARGET_WIDTH && a.height === this.TARGET_HEIGHT;
      const bExactDimensions = b.width === this.TARGET_WIDTH && b.height === this.TARGET_HEIGHT;
      
      if (aExactDimensions && !bExactDimensions) return -1;
      if (!aExactDimensions && bExactDimensions) return 1;

      // Priority 2: Score (higher is better)
      if (a.score !== b.score) return b.score - a.score;

      // Priority 3: Aspect ratio (closer to target is better)
      const aAspectRatio = a.width / a.height;
      const bAspectRatio = b.width / b.height;
      const aAspectDiff = Math.abs(aAspectRatio - targetAspectRatio);
      const bAspectDiff = Math.abs(bAspectRatio - targetAspectRatio);
      
      return aAspectDiff - bAspectDiff;
    })[0];
  }

  /**
   * Download and process hero banner image
   */
  private static async downloadAndProcessHero(
    hero: SteamGridDBHero,
    gameId: string,
    gameName: string
  ): Promise<{ success: boolean; heroUrl?: string; fileSize?: number; error?: string }> {
    try {
      // Download the image
      const response = await fetch(hero.url);
      
      if (!response.ok) {
        throw new Error(`Failed to download hero: ${response.status} ${response.statusText}`);
      }

      const imageBuffer = await response.arrayBuffer();
      const buffer = Buffer.from(imageBuffer);

      // Check file size
      if (buffer.length > this.MAX_FILE_SIZE) {
        throw new Error(`Hero banner too large: ${buffer.length} bytes (max: ${this.MAX_FILE_SIZE})`);
      }

      // Optimize the image
      const optimized = await ImageOptimizer.optimizeImage(buffer, {
        width: this.TARGET_WIDTH,
        height: this.TARGET_HEIGHT,
        quality: 85,
        format: 'webp',
        stripMetadata: true
      });

      // Generate filename
      const filename = `${gameId}-${hero.id}-${Date.now()}.webp`;
      
      // Upload to Supabase storage
      const { data, error } = await supabase.storage
        .from(this.HERO_BUCKET)
        .upload(filename, optimized.buffer, {
          contentType: 'image/webp',
          cacheControl: '31536000', // 1 year cache
          upsert: false
        });

      if (error) {
        throw new Error(`Failed to upload to storage: ${error.message}`);
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from(this.HERO_BUCKET)
        .getPublicUrl(filename);

      return {
        success: true,
        heroUrl: urlData.publicUrl,
        fileSize: optimized.size
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error during processing'
      };
    }
  }

  /**
   * Update hero cache status in database
   */
  private static async updateHeroCacheStatus(
    gameId: string,
    status: string,
    cachedUrl?: string,
    authorName?: string,
    authorSteam64?: string | null,
    authorAvatar?: string | null,
    heroId?: number,
    sgdbGameId?: number
  ): Promise<void> {
    const { error } = await supabase.rpc('update_steamgriddb_hero_cache_status', {
      game_id: gameId,
      new_status: status,
      cached_url: cachedUrl || null,
      author_name: authorName || null,
      author_steam64: authorSteam64 || null,
      author_avatar: authorAvatar || null,
      hero_id: heroId || null,
      sgdb_game_id: sgdbGameId || null
    });

    if (error) {
      console.error('Failed to update hero cache status:', error);
    }
  }

  /**
   * Log audit event
   */
  private static async logAudit(
    gameId: string,
    action: string,
    steamgriddbGameId?: number | null,
    steamgriddbHeroId?: number | null,
    originalUrl?: string | null,
    supabaseUrl?: string | null,
    authorName?: string | null,
    authorSteam64?: string | null,
    errorMessage?: string | null,
    processingTimeMs?: number | null,
    fileSizeBytes?: number | null,
    dimensions?: string | null
  ): Promise<void> {
    const { error } = await supabase.rpc('log_steamgriddb_hero_audit', {
      p_game_id: gameId,
      p_action: action,
      p_steamgriddb_game_id: steamgriddbGameId,
      p_steamgriddb_hero_id: steamgriddbHeroId,
      p_original_url: originalUrl,
      p_supabase_url: supabaseUrl,
      p_author_name: authorName,
      p_author_steam64: authorSteam64,
      p_error_message: errorMessage,
      p_processing_time_ms: processingTimeMs,
      p_file_size_bytes: fileSizeBytes,
      p_dimensions: dimensions
    });

    if (error) {
      console.error('Failed to log hero audit:', error);
    }
  }

  /**
   * Get cached hero information for a game
   */
  static async getCachedHeroInfo(gameId: string): Promise<{
    heroUrl?: string;
    credits?: any;
    status: string;
  }> {
    const { data, error } = await supabase
      .from('games')
      .select(`
        steamgriddb_hero_url,
        steamgriddb_hero_cache_status,
        steamgriddb_hero_author_name,
        steamgriddb_hero_author_steam64,
        steamgriddb_hero_author_avatar,
        steamgriddb_hero_id,
        steamgriddb_game_id
      `)
      .eq('id', gameId)
      .single();

    if (error || !data) {
      return { status: 'pending' };
    }

    const credits = data.steamgriddb_hero_url ? {
      source: 'SteamGridDB',
      authorName: data.steamgriddb_hero_author_name,
      authorSteam64: data.steamgriddb_hero_author_steam64,
      authorAvatar: data.steamgriddb_hero_author_avatar,
      heroId: data.steamgriddb_hero_id,
      gameId: data.steamgriddb_game_id
    } : undefined;

    return {
      heroUrl: data.steamgriddb_hero_url || undefined,
      credits,
      status: data.steamgriddb_hero_cache_status || 'pending'
    };
  }
}
