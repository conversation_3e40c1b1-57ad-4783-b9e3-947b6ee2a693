import puppeteer from 'puppeteer';
import { createServerClient } from '@/lib/supabase/server';
import { steamApiService } from './steamApiService';
import { kinguinApiService, type KinguinGamePrice } from './kinguinApiService';
import { cjRestApiService, CJGamePrice } from './cjRestApiService';
import { DEFAULT_REGION } from '@/lib/constants/steamRegions';

interface PriceData {
  store_name: string;
  price: string;
  original_price?: string;
  discount_percentage?: number;
  store_url: string;
  currency: string;
  availability: 'available' | 'out_of_stock' | 'pre_order';
  region_code: string; // Added for multi-region support
  affiliate_url?: string; // Added for affiliate tracking
}

interface ScrapingResult {
  success: boolean;
  data?: PriceData[];
  error?: string;
}

export class PriceScrapingService {
  private browser: any = null;

  async init() {
    if (!this.browser) {
      this.browser = await puppeteer.launch({
        headless: true, // Use headless mode
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor',
          '--disable-blink-features=AutomationControlled',
          '--no-first-run',
          '--disable-default-apps',
          '--disable-dev-shm-usage',
          '--window-size=1920,1080'
        ],
        ignoreDefaultArgs: ['--enable-automation'],
        defaultViewport: { width: 1920, height: 1080 }
      });
    }
  }

  async close() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }

  /**
   * Format price value with currency symbol
   */
  formatPrice(price: number, currency: string): string {
    const currencySymbols: Record<string, string> = {
      'USD': '$',
      'EUR': '€',
      'GBP': '£',
      'JPY': '¥',
      'KRW': '₩',
      'CNY': '¥',
      'RUB': '₽',
      'BRL': 'R$',
      'MXN': 'M$',
      'CAD': 'C$',
      'AUD': 'A$',
      'INR': '₹'
    };

    const symbol = currencySymbols[currency] || '$';
    
    // For currencies that don't typically use decimals (Asian currencies)
    const noDecimalCurrencies = ['JPY', 'KRW', 'CNY'];
    
    if (noDecimalCurrencies.includes(currency)) {
      return `${symbol}${Math.round(price)}`;
    } else {
      return `${symbol}${price.toFixed(2)}`;
    }
  }

  async scrapeSteam(gameName: string, regionCode: string = DEFAULT_REGION): Promise<PriceData | null> {
    try {
      console.log(`🎮 Steam API: Fetching price for "${gameName}" in region ${regionCode.toUpperCase()}`);
      
      // Use Steam API instead of scraping
      const steamPriceData = await steamApiService.getGamePrice(gameName, regionCode);
      
      if (!steamPriceData) {
        console.log(`❌ Steam API: No price data found for "${gameName}" in region ${regionCode}`);
        return null;
      }

      console.log(`✅ Steam API: Found price for "${gameName}" in ${regionCode.toUpperCase()}: ${steamPriceData.price}`);
      
      return {
        store_name: steamPriceData.store_name,
        price: steamPriceData.price,
        original_price: steamPriceData.original_price,
        discount_percentage: steamPriceData.discount_percentage,
        store_url: steamPriceData.store_url,
        currency: steamPriceData.currency,
        availability: steamPriceData.availability,
        region_code: steamPriceData.region_code
      };
    } catch (error) {
      console.error(`Erro ao buscar preço no Steam via API para região ${regionCode}:`, error);
      return null;
    }
  }

  /**
   * Get Steam prices for multiple regions
   */
  async scrapeSteamMultiRegion(gameName: string, regionCodes: string[]): Promise<PriceData[]> {
    try {
      console.log(`🌍 Steam: Fetching "${gameName}" prices in ${regionCodes.length} regions`);
      
      const steamPrices = await steamApiService.getGamePriceMultiRegion(gameName, regionCodes);
      
      return steamPrices.map(steamPrice => ({
        store_name: steamPrice.store_name,
        price: steamPrice.price,
        original_price: steamPrice.original_price,
        discount_percentage: steamPrice.discount_percentage,
        store_url: steamPrice.store_url,
        currency: steamPrice.currency,
        availability: steamPrice.availability,
        region_code: steamPrice.region_code
      }));
    } catch (error) {
      console.error('Erro ao buscar preços Steam multi-região:', error);
      return [];
    }
  }

  async scrapeNuuvem(gameName: string, regionCode: string = 'br'): Promise<PriceData | null> {
    try {
      const page = await this.browser.newPage();
      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
      
      const searchUrl = `https://www.nuuvem.com/catalog/search/${encodeURIComponent(gameName)}`;
      await page.goto(searchUrl, { waitUntil: 'networkidle2' });

      // Aguardar carregamento com seletores múltiplos e timeout maior
      try {
        await page.waitForSelector('.product-card, .product-item, .game-card, [data-testid="product"], .card', { timeout: 15000 });
      } catch (timeoutError) {
        console.log('⚠️ Nuuvem: Nenhum produto encontrado ou página não carregou completamente');
        await page.close();
        return null;
      }

      const gameData = await page.evaluate(() => {
        // Tentar múltiplos seletores para encontrar produtos
        const firstProduct = document.querySelector('.product-card') || 
                            document.querySelector('.product-item') || 
                            document.querySelector('.game-card') || 
                            document.querySelector('[data-testid="product"]') ||
                            document.querySelector('.card');
        
        if (!firstProduct) return null;

        // Tentar múltiplos seletores para título
        const titleElement = firstProduct.querySelector('.product-title') ||
                            firstProduct.querySelector('.title') ||
                            firstProduct.querySelector('.game-title') ||
                            firstProduct.querySelector('h3') ||
                            firstProduct.querySelector('h2');
        
        // Tentar múltiplos seletores para preço
        const priceElement = firstProduct.querySelector('.product-price') ||
                            firstProduct.querySelector('.price') ||
                            firstProduct.querySelector('.cost') ||
                            firstProduct.querySelector('[class*="price"]');
        
        const linkElement = firstProduct.querySelector('a');

        if (!titleElement || !priceElement || !linkElement) return null;

        const title = titleElement.textContent?.trim();
        const priceText = priceElement.textContent?.trim();
        const href = linkElement.getAttribute('href');

        // Extrair preços
        const priceMatch = priceText?.match(/R\$\s*[\d,]+/g);
        let currentPrice = '';
        let originalPrice = '';

        if (priceMatch) {
          currentPrice = priceMatch[priceMatch.length - 1];
          if (priceMatch.length > 1) {
            originalPrice = priceMatch[0];
          }
        }

        return {
          title,
          currentPrice,
          originalPrice,
          url: href ? `https://www.nuuvem.com${href}` : ''
        };
      });

      await page.close();

      if (!gameData || !gameData.currentPrice) return null;

      // Calcular desconto
      let discountPercentage = 0;
      if (gameData.originalPrice && gameData.currentPrice) {
        const original = parseFloat(gameData.originalPrice.replace(/[R$\s,]/g, ''));
        const current = parseFloat(gameData.currentPrice.replace(/[R$\s,]/g, ''));
        discountPercentage = Math.round(((original - current) / original) * 100);
      }

      return {
        store_name: 'Nuuvem',
        price: gameData.currentPrice,
        original_price: gameData.originalPrice || undefined,
        discount_percentage: discountPercentage > 0 ? discountPercentage : undefined,
        store_url: gameData.url,
        currency: 'BRL',
        availability: 'available',
        region_code: regionCode // Nuuvem is Brazil-only, so always 'br'
      };
    } catch (error) {
      console.error('Erro ao fazer scraping da Nuuvem:', error);
      return null;
    }
  }

  async scrapeEpicGames(gameName: string, regionCode: string = 'us'): Promise<PriceData | null> {
    try {
      const page = await this.browser.newPage();
      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
      
      const searchUrl = `https://store.epicgames.com/en-US/browse?q=${encodeURIComponent(gameName)}`;
      await page.goto(searchUrl, { waitUntil: 'networkidle2' });

      // Aguardar carregamento
      await new Promise(resolve => setTimeout(resolve, 3000));

      const gameData = await page.evaluate(() => {
        // Epic Games usa React, então precisamos aguardar o carregamento
        const productCard = document.querySelector('[data-testid="offer-card"]');
        if (!productCard) return null;

        const titleElement = productCard.querySelector('[data-testid="offer-title-info-title"]');
        const priceElement = productCard.querySelector('[data-testid="price-display"]');
        const linkElement = productCard.querySelector('a');

        if (!titleElement || !linkElement) return null;

        const title = titleElement.textContent?.trim();
        const priceText = priceElement?.textContent?.trim();
        const href = linkElement.getAttribute('href');

        // Epic Games pode ter jogos gratuitos
        let currentPrice = '';
        let originalPrice = '';
        let isFree = false;

        if (priceText) {
          if (priceText.toLowerCase().includes('free')) {
            currentPrice = 'Free';
            isFree = true;
          } else {
            // Tentar extrair preços USD
            const priceMatch = priceText.match(/\$[\d.]+/g);
            if (priceMatch) {
              currentPrice = priceMatch[priceMatch.length - 1];
              if (priceMatch.length > 1) {
                originalPrice = priceMatch[0];
              }
            }
          }
        }

        return {
          title,
          currentPrice,
          originalPrice,
          isFree,
          url: href ? `https://store.epicgames.com${href}` : ''
        };
      });

      await page.close();

      if (!gameData || (!gameData.currentPrice && !gameData.isFree)) return null;

      // Calcular desconto
      let discountPercentage = 0;
      if (gameData.originalPrice && gameData.currentPrice && !gameData.isFree) {
        const original = parseFloat(gameData.originalPrice.replace(/[$]/g, ''));
        const current = parseFloat(gameData.currentPrice.replace(/[$]/g, ''));
        discountPercentage = Math.round(((original - current) / original) * 100);
      }

      return {
        store_name: 'Epic Games',
        price: gameData.isFree ? 'Gratuito' : gameData.currentPrice,
        original_price: gameData.originalPrice || undefined,
        discount_percentage: discountPercentage > 0 ? discountPercentage : undefined,
        store_url: gameData.url,
        currency: gameData.isFree ? 'USD' : 'USD',
        availability: 'available',
        region_code: regionCode // Epic Games typically uses USD/US region
      };
    } catch (error) {
      console.error('Erro ao fazer scraping do Epic Games:', error);
      return null;
    }
  }

  async scrapeInstantGaming(gameName: string, regionCode: string = 'us'): Promise<PriceData | null> {
    try {
      const page = await this.browser.newPage();
      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
      
      const searchUrl = `https://www.instant-gaming.com/en/search/?q=${encodeURIComponent(gameName)}`;
      await page.goto(searchUrl, { waitUntil: 'networkidle2' });

      // Aguardar carregamento específico do Instant Gaming
      await new Promise(resolve => setTimeout(resolve, 2000));

      const gameData = await page.evaluate(() => {
        const firstProduct = document.querySelector('.item');
        if (!firstProduct) return null;

        const titleElement = firstProduct.querySelector('.title');
        const priceElement = firstProduct.querySelector('.price');
        const discountElement = firstProduct.querySelector('.discount');
        const linkElement = firstProduct.querySelector('a');

        if (!titleElement || !priceElement || !linkElement) return null;

        const title = titleElement.textContent?.trim();
        const priceText = priceElement.textContent?.trim();
        const discountText = discountElement?.textContent?.trim();
        const href = linkElement.getAttribute('href');

        // Instant Gaming sempre tem preços em USD (ou EUR dependendo da região)
        const priceMatch = priceText?.match(/\$[\d.]+/g) || priceText?.match(/€[\d.]+/g);
        let currentPrice = '';
        let discountPercentage = 0;

        if (priceMatch) {
          currentPrice = priceMatch[0];
        }

        // Extrair desconto se disponível
        if (discountText) {
          const discountMatch = discountText.match(/(\d+)%/);
          if (discountMatch) {
            discountPercentage = parseInt(discountMatch[1]);
          }
        }

        return {
          title,
          currentPrice,
          discountPercentage,
          url: href ? `https://www.instant-gaming.com${href}` : ''
        };
      });

      await page.close();

      if (!gameData || !gameData.currentPrice) return null;

      return {
        store_name: 'Instant Gaming',
        price: gameData.currentPrice,
        original_price: undefined, // Instant Gaming não mostra preço original claramente
        discount_percentage: gameData.discountPercentage > 0 ? gameData.discountPercentage : undefined,
        store_url: gameData.url,
        currency: gameData.currentPrice.includes('€') ? 'EUR' : 'USD',
        availability: 'available',
        region_code: regionCode
      };
    } catch (error) {
      console.error('Erro ao fazer scraping do Instant Gaming:', error);
      return null;
    }
  }

  async scrapeKinguin(gameName: string, regionCode: string = 'global'): Promise<PriceData | null> {
    console.log(`🚀🚀🚀 KINGUIN SCRAPER DEFINITELY CALLED for "${gameName}" in region ${regionCode} 🚀🚀🚀`);
    
    try {
      console.log(`🔧 Checking if Kinguin is configured...`);
      
      // Check if Kinguin service is configured
      if (!kinguinApiService.isConfigured()) {
        console.error(`❌❌❌ KINGUIN NOT CONFIGURED - MISSING API KEY OR AFFILIATE ID ❌❌❌`);
        console.error(`Current config:`, {
          hasApiKey: !!process.env.KINGUIN_API_KEY,
          hasAffiliateId: !!process.env.KINGUIN_AFFILIATE_ID,
          apiKeyLength: process.env.KINGUIN_API_KEY?.length || 0,
          affiliateId: process.env.KINGUIN_AFFILIATE_ID
        });
        return null;
      }
      
      console.log(`✅ Kinguin is configured, calling getGamePrice for region ${regionCode}...`);
      
      // Use Kinguin API 
      const kinguinData = await kinguinApiService.getGamePrice(gameName, regionCode);
      
      console.log(`🔍 Kinguin getGamePrice returned for region ${regionCode}:`, kinguinData);
      
      if (!kinguinData) {
        console.error(`❌ Kinguin API returned null for "${gameName}" in region ${regionCode}`);
        return null;
      }

      console.log(`✅ Kinguin found price for region ${regionCode}: ${kinguinData.currency} ${kinguinData.price}`);
      
      // Note: Kinguin handles its own currency conversion internally
      // Other stores will use their native regional pricing without conversion
      const priceData = {
        store_name: 'Kinguin',
        price: kinguinApiService.formatPrice(kinguinData.price, kinguinData.currency),
        original_price: kinguinData.originalPrice ? 
          kinguinApiService.formatPrice(kinguinData.originalPrice, kinguinData.currency) : undefined,
        discount_percentage: kinguinData.discountPercentage,
        store_url: kinguinData.storeUrl,
        affiliate_url: kinguinData.affiliateUrl,
        currency: kinguinData.currency,
        availability: kinguinData.availability,
        region_code: regionCode
      };
      
      console.log(`🎯 FINAL KINGUIN PRICE DATA for region ${regionCode}:`, priceData);
      
      return priceData;
      
    } catch (error) {
      console.error(`💥💥💥 KINGUIN SCRAPER ERROR for region ${regionCode}:`, error);
      console.error(`Error details:`, {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });
      return null;
    }
  }

  /**
   * NEW: Scrape Kinguin prices for ALL supported currencies at once
   * This method fetches and returns price data for all currencies simultaneously
   */
  async scrapeKinguinAllCurrencies(gameName: string): Promise<PriceData[]> {
    console.log(`🌍🚀 KINGUIN MULTI-CURRENCY SCRAPER for "${gameName}" - fetching ALL currencies 🚀🌍`);
    
    try {
      // Check if Kinguin service is configured
      if (!kinguinApiService.isConfigured()) {
        console.error(`❌ KINGUIN NOT CONFIGURED - MISSING API KEY OR AFFILIATE ID`);
        return [];
      }
      
      console.log(`✅ Kinguin configured, fetching prices in ALL supported currencies...`);
      
      // Get prices for all currencies from Kinguin API
      const allCurrencyPrices = await kinguinApiService.getGamePricesAllCurrencies(gameName);
      
      if (!allCurrencyPrices || allCurrencyPrices.length === 0) {
        console.error(`❌ Kinguin multi-currency API returned no prices for "${gameName}"`);
        return [];
      }

      console.log(`✅ Kinguin found prices in ${allCurrencyPrices.length} currencies for "${gameName}"`);
      
      // Convert to PriceData format for each currency
      const priceDataArray: PriceData[] = allCurrencyPrices.map(kinguinData => {
        const priceData = {
          store_name: 'Kinguin',
          price: kinguinApiService.formatPrice(kinguinData.price, kinguinData.currency),
          original_price: kinguinData.originalPrice ? 
            kinguinApiService.formatPrice(kinguinData.originalPrice, kinguinData.currency) : undefined,
          discount_percentage: kinguinData.discountPercentage,
          store_url: kinguinData.storeUrl,
          affiliate_url: kinguinData.affiliateUrl,
          currency: kinguinData.currency,
          availability: kinguinData.availability,
          region_code: kinguinData.region
        };
        
        console.log(`💰 Kinguin ${kinguinData.currency} (${kinguinData.region}): ${priceData.price}`);
        return priceData;
      });
      
      console.log(`🎯 KINGUIN MULTI-CURRENCY COMPLETE: Generated ${priceDataArray.length} price records`);
      
      return priceDataArray;
      
    } catch (error) {
      console.error(`💥 KINGUIN MULTI-CURRENCY SCRAPER ERROR:`, error);
      console.error(`Error details:`, {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });
      return [];
    }
  }

  /**
   * Scrape Fanatical prices via CJ API
   */
  async scrapeFanatical(gameName: string, regionCode: string = 'global'): Promise<PriceData | null> {
    console.log(`🚀 FANATICAL (CJ) SCRAPER: Searching for "${gameName}" in region ${regionCode}`);
    
    try {
      if (!cjRestApiService.isConfigured()) {
        console.error(`❌ CJ API not configured - missing credentials`);
        return null;
      }
      
      const cjPrices = await cjRestApiService.searchGames(gameName, regionCode);
      
      if (!cjPrices || cjPrices.length === 0) {
        console.error(`❌ CJ/Fanatical API returned no prices for "${gameName}"`);
        return null;
      }

      // Find Fanatical specifically, or use the first result
      const fanaticalData = cjPrices.find(p => p.store_name.toLowerCase().includes('fanatical')) || cjPrices[0];

      console.log(`✅ CJ/Fanatical found price: ${fanaticalData.currency} ${fanaticalData.price}`);
      
      return {
        store_name: fanaticalData.store_name,
        price: this.formatPrice(fanaticalData.price, fanaticalData.currency),
        original_price: fanaticalData.original_price ? 
          this.formatPrice(fanaticalData.original_price, fanaticalData.currency) : undefined,
        discount_percentage: fanaticalData.discount_percentage,
        store_url: fanaticalData.store_url,
        affiliate_url: fanaticalData.affiliate_url,
        currency: fanaticalData.currency,
        availability: fanaticalData.availability,
        region_code: regionCode
      };
    } catch (error) {
      console.error(`💥 FANATICAL (CJ) SCRAPER ERROR:`, error);
      return null;
    }
  }

  /**
   * Scrape Gamivo prices via direct web scraping
   */
  async scrapeGamivoWebScraping(gameName: string, regionCode: string = 'us'): Promise<PriceData | null> {
    console.log(`🚀 GAMIVO WEB SCRAPER: Searching for "${gameName}" in region ${regionCode}`);
    
    try {
      const page = await this.browser.newPage();
      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
      
      // Gamivo search URL
      const searchUrl = `https://www.gamivo.com/search?query=${encodeURIComponent(gameName)}`;
      await page.goto(searchUrl, { waitUntil: 'networkidle2', timeout: 30000 });

      // Wait for content to load and check if page loaded successfully
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      // Check page status before evaluating
      const pageStatus = await page.evaluate(() => {
        return {
          url: window.location.href,
          title: document.title,
          hasContent: document.body.innerHTML.length > 100,
          bodyPreview: document.body.innerHTML.substring(0, 500)
        };
      });
      
      console.log(`📄 Gamivo page status:`, pageStatus);

      const gameData = await page.evaluate((searchQuery) => {
        // Log to both console and return value so we can see it in server logs
        const log = (msg: string) => {
          console.log(msg);
          // Also try to add to window for debugging
          if (typeof window !== 'undefined') {
            (window as any).debugLogs = (window as any).debugLogs || [];
            (window as any).debugLogs.push(msg);
          }
        };
        
        log(`🔍 Gamivo Debug: Starting evaluation for "${searchQuery}"`);
        
        // First, check if we're on the right page
        const currentUrl = window.location.href;
        const pageTitle = document.title;
        log(`📍 Current URL: ${currentUrl}`);
        log(`📝 Page Title: ${pageTitle}`);
        
        // Check for common e-commerce layout patterns
        const allElements = document.querySelectorAll('*[class]');
        const classNames = new Set<string>();
        allElements.forEach(el => {
          if (el.className && typeof el.className === 'string') {
            el.className.split(' ').forEach(cls => {
              if (cls.trim()) classNames.add(cls.trim());
            });
          }
        });
        
        // Look for product-related classes
        const productClasses = Array.from(classNames).filter(cls => 
          cls.toLowerCase().includes('product') ||
          cls.toLowerCase().includes('game') ||
          cls.toLowerCase().includes('item') ||
          cls.toLowerCase().includes('card') ||
          cls.toLowerCase().includes('result') ||
          cls.toLowerCase().includes('offer')
        );
        
        console.log(`🏷️ Found ${productClasses.length} product-related classes:`, productClasses.slice(0, 10));
        
        // Enhanced selectors based on common e-commerce patterns
        const selectors = [
          // Gamivo specific (if they use these)
          '.product-card', '.product-item', '.game-card', '.offer-card',
          // Generic e-commerce patterns
          '.product', '.item', '.card', '.result', '.listing',
          // React/Vue component patterns
          '[data-testid*="product"]', '[data-qa*="product"]', '[data-cy*="product"]',
          // CSS class patterns
          '[class*="Product"]', '[class*="Item"]', '[class*="Card"]', '[class*="Game"]',
          // Semantic elements
          'article', 'section[class*="product"]', 'div[class*="product"]',
          // Grid/list items
          '.grid-item', '.list-item', '.search-item'
        ];
        
        let productCards: Element[] = [];
        let matchedSelector = '';
        
        for (const selector of selectors) {
          try {
            const cards = document.querySelectorAll(selector);
            if (cards.length > 0) {
              productCards = Array.from(cards);
              matchedSelector = selector;
              console.log(`🎯 Gamivo Debug: Found ${cards.length} elements with selector "${selector}"`);
              break;
            }
          } catch (e) {
            console.log(`⚠️ Invalid selector: ${selector}`);
          }
        }
        
        if (productCards.length === 0) {
          // Fallback: look for any elements that might contain game titles and prices
          console.log(`🔍 No standard product cards found, searching for any elements with text...`);
          
          const fallbackElements = document.querySelectorAll('div, article, section, li');
          const candidateElements = Array.from(fallbackElements).filter(el => {
            const text = el.textContent?.trim() || '';
            const hasGameKeywords = /\b(game|edition|dlc|pack)\b/i.test(text);
            const hasPricePattern = /[$€£¥₽]\s*\d+|USD|EUR|GBP|JPY|RUB/i.test(text);
            return hasGameKeywords && hasPricePattern && text.length > 10 && text.length < 500;
          });
          
          if (candidateElements.length > 0) {
            productCards = candidateElements.slice(0, 10); // Limit to first 10
            matchedSelector = 'fallback-elements';
            console.log(`🎯 Fallback: Found ${candidateElements.length} potential product elements`);
          } else {
            console.log(`❌ Gamivo Debug: No product cards found with any method.`);
            console.log(`📋 Page structure sample:`, document.body.innerHTML.substring(0, 1000));
            return null;
          }
        }
        
        console.log(`✅ Gamivo Debug: Found ${productCards.length} product cards total`);

        // Find the best match for the game name
        let bestMatch = null;
        let bestScore = 0;

        for (const card of productCards) {
          // Expanded selectors for title elements
          const titleSelectors = [
            '.product-title', '.card-title', '.product-name', '.game-title', '.offer-title',
            'h1', 'h2', 'h3', 'h4', 'h5', '.title', '.name', '[class*="title"]', '[class*="name"]'
          ];
          
          let titleElement = null;
          for (const selector of titleSelectors) {
            titleElement = card.querySelector(selector);
            if (titleElement) break;
          }
          
          if (!titleElement) {
            console.log(`⚠️ Gamivo Debug: No title element found in card`);
            continue;
          }

          const title = titleElement.textContent?.trim().toLowerCase() || '';
          const queryLower = searchQuery.toLowerCase();
          
          console.log(`🔍 Gamivo Debug: Checking card title: "${title}" vs query: "${queryLower}"`);
          
          // Simple scoring: exact match gets highest score, partial matches get lower scores
          let score = 0;
          if (title === queryLower) {
            score = 100;
          } else if (title.includes(queryLower)) {
            score = 80;
          } else if (queryLower.includes(title)) {
            score = 60;
          } else {
            // Check for word matches
            const titleWords = title.split(' ');
            const queryWords = queryLower.split(' ');
            const matchingWords = titleWords.filter(word => 
              queryWords.some(qWord => qWord.includes(word) || word.includes(qWord))
            );
            score = (matchingWords.length / Math.max(titleWords.length, queryWords.length)) * 50;
          }

          if (score > bestScore) {
            bestScore = score;
            bestMatch = card;
          }
        }

        if (!bestMatch || bestScore < 30) {
          console.log(`❌ Gamivo Debug: No good match found. Best score: ${bestScore}`);
          return null;
        }
        
        console.log(`✅ Gamivo Debug: Best match found with score ${bestScore}`);

        // Extract price information from the best match
        console.log(`💰 Extracting price data from best match element...`);
        
        // Enhanced title selectors
        const titleSelectors = [
          '.product-title', '.card-title', '.product-name', '.game-title', '.title', '.name',
          'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
          '[class*="title"]', '[class*="name"]', '[class*="Title"]', '[class*="Name"]'
        ];
        
        let titleElement = null;
        for (const selector of titleSelectors) {
          titleElement = bestMatch.querySelector(selector);
          if (titleElement) {
            console.log(`📝 Found title element with selector: ${selector}`);
            break;
          }
        }
        
        // Enhanced price selectors with more patterns
        const priceSelectors = [
          '.price', '.product-price', '.current-price', '.final-price', '.cost', '.amount', '.value',
          '[class*="price"]', '[class*="Price"]', '[class*="cost"]', '[class*="Cost"]', 
          '[class*="amount"]', '[class*="Amount"]', '[data-price]', '[data-cost]',
          '.pricing', '.price-current', '.price-new', '.sale-price'
        ];
        
        let priceElement = null;
        let priceSelector = '';
        for (const selector of priceSelectors) {
          priceElement = bestMatch.querySelector(selector);
          if (priceElement) {
            priceSelector = selector;
            console.log(`💰 Gamivo Debug: Found price element with selector "${selector}"`);
            break;
          }
        }
        
        // If no dedicated price element, look for text patterns in the entire element
        if (!priceElement) {
          console.log(`🔍 No dedicated price element found, searching for price patterns in text...`);
          const allTextElements = bestMatch.querySelectorAll('*');
          for (const el of allTextElements) {
            const text = el.textContent?.trim() || '';
            if (/[$€£¥₽]\s*[\d,.]+(\.|\,)?\d*|(\d+[\.,])?\d+\s*(USD|EUR|GBP|JPY|RUB)/i.test(text)) {
              priceElement = el;
              priceSelector = 'text-pattern-match';
              console.log(`💰 Found price pattern in element: "${text.substring(0, 50)}"`);
              break;
            }
          }
        }
        
        const originalPriceElement = bestMatch.querySelector('.original-price, .old-price, .was-price, .retail-price, [class*="original"], [class*="old"], [class*="was"], [class*="retail"]');
        const linkElement = bestMatch.querySelector('a') || bestMatch.closest('a');
        
        console.log(`🔗 Link element found: ${!!linkElement}`);

        const title = titleElement?.textContent?.trim() || '';
        const priceText = priceElement?.textContent?.trim() || '';
        const originalPriceText = originalPriceElement?.textContent?.trim() || '';
        const href = linkElement?.getAttribute('href') || '';

        let currentPrice = '';
        let originalPrice = '';
        let currency = 'USD';

        console.log(`💰 Raw price text: "${priceText}"`);
        console.log(`💰 Raw original price text: "${originalPriceText}"`);
        
        if (priceText) {
          // Enhanced price extraction with multiple patterns
          const pricePatterns = [
            // Currency symbol + number: $19.99, €19,99, £19.99
            /([€$£¥₽₩])\s*([0-9]+[,.]\d{2}|[0-9]+)/i,
            // Number + currency code: 19.99 USD, 19,99 EUR
            /([0-9]+[,.]\d{2}|[0-9]+)\s*(USD|EUR|GBP|JPY|RUB|KRW|CAD|AUD)/i,
            // Currency code + number: USD 19.99, EUR 19,99
            /(USD|EUR|GBP|JPY|RUB|KRW|CAD|AUD)\s*([0-9]+[,.]\d{2}|[0-9]+)/i,
            // Just numbers with decimals (assume USD): 19.99
            /([0-9]+\.[0-9]{2})/,
            // Just whole numbers: 19
            /([0-9]+)/
          ];
          
          let priceMatch = null;
          for (const pattern of pricePatterns) {
            priceMatch = priceText.match(pattern);
            if (priceMatch) {
              console.log(`💰 Price matched pattern: ${pattern}`);
              break;
            }
          }
          
          if (priceMatch) {
            let currencySymbol = '';
            let priceValue = '';
            
            // Handle different match groups based on pattern
            if (priceMatch[1] && /[€$£¥₽₩]/.test(priceMatch[1])) {
              // Currency symbol first
              currencySymbol = priceMatch[1];
              priceValue = priceMatch[2];
            } else if (priceMatch[2] && /USD|EUR|GBP|JPY|RUB|KRW|CAD|AUD/i.test(priceMatch[2])) {
              // Number first, currency code second
              priceValue = priceMatch[1];
              currencySymbol = priceMatch[2].toUpperCase();
            } else if (priceMatch[1] && /USD|EUR|GBP|JPY|RUB|KRW|CAD|AUD/i.test(priceMatch[1])) {
              // Currency code first
              currencySymbol = priceMatch[1].toUpperCase();
              priceValue = priceMatch[2];
            } else {
              // Just number - assume USD
              priceValue = priceMatch[1];
              currencySymbol = '$';
            }
            
            // Map currency symbols to codes
            const currencyMap: Record<string, string> = {
              '$': 'USD', 'USD': 'USD',
              '€': 'EUR', 'EUR': 'EUR', 
              '£': 'GBP', 'GBP': 'GBP',
              '¥': 'JPY', 'JPY': 'JPY', '¥': 'CNY',
              '₽': 'RUB', 'RUB': 'RUB',
              '₩': 'KRW', 'KRW': 'KRW',
              'CAD': 'CAD', 'AUD': 'AUD'
            };
            
            currency = currencyMap[currencySymbol] || currencyMap[currencySymbol.toUpperCase()] || 'USD';
            
            // Format the price display
            if (currency === currencySymbol.toUpperCase()) {
              // Currency code format
              currentPrice = `${priceValue} ${currency}`;
            } else {
              // Symbol format
              currentPrice = `${currencySymbol}${priceValue}`;
            }
            
            console.log(`💰 Extracted price: ${currentPrice}, currency: ${currency}`);
          } else {
            console.log(`❌ No price pattern matched in: "${priceText}"`);
          }
        }

        if (originalPriceText) {
          const originalMatch = originalPriceText.match(/([€$£¥₽]|USD|EUR|GBP|JPY|RUB)\s*([0-9,.]+)/i);
          if (originalMatch) {
            originalPrice = `${originalMatch[1]}${originalMatch[2]}`;
          }
        }

        // Calculate discount if both prices exist
        let discountPercentage = 0;
        if (currentPrice && originalPrice) {
          const currentNum = parseFloat(currentPrice.replace(/[^0-9.]/g, ''));
          const originalNum = parseFloat(originalPrice.replace(/[^0-9.]/g, ''));
          if (currentNum > 0 && originalNum > currentNum) {
            discountPercentage = Math.round(((originalNum - currentNum) / originalNum) * 100);
          }
        }

        return {
          title,
          currentPrice,
          originalPrice: originalPrice || undefined,
          discountPercentage: discountPercentage > 0 ? discountPercentage : undefined,
          currency,
          href: href.startsWith('http') ? href : `https://www.gamivo.com${href}`,
          availability: 'available',
          debugLogs: (window as any).debugLogs || []
        };
      }, gameName);

      await page.close();

      // Log debug information from the page
      if (gameData && gameData.debugLogs) {
        console.log(`🔍 Gamivo Debug Logs:`, gameData.debugLogs);
      }

      if (!gameData || !gameData.currentPrice) {
        console.log(`⚠️ Gamivo: No price found for "${gameName}"`);
        if (gameData && gameData.debugLogs) {
          console.log(`📝 Last few debug logs:`, gameData.debugLogs.slice(-10));
        }
        return null;
      }

      console.log(`✅ Gamivo found: ${gameData.title} - ${gameData.currentPrice} (${gameData.currency})`);

      return {
        store_name: 'Gamivo',
        price: gameData.currentPrice,
        original_price: gameData.originalPrice,
        discount_percentage: gameData.discountPercentage,
        store_url: gameData.href,
        affiliate_url: gameData.href, // Gamivo direct URL (no affiliate program)
        currency: gameData.currency,
        availability: gameData.availability as 'available',
        region_code: regionCode
      };

    } catch (error) {
      console.error(`💥 GAMIVO WEB SCRAPER ERROR:`, error);
      return null;
    }
  }

  /**
   * Scrape Gamivo prices via CJ API
   */
  async scrapeGamivoCJ(gameName: string, regionCode: string = 'global'): Promise<PriceData | null> {
    console.log(`🚀 GAMIVO (CJ REST API) SCRAPER: Searching for "${gameName}" in region ${regionCode}`);
    
    try {
      if (!cjRestApiService.isConfigured()) {
        console.log(`⚠️ CJ REST API not configured for Gamivo - skipping`);
        return null;
      }

      // Get prices from CJ REST API for Gamivo and other gaming stores
      const cjPrices = await cjRestApiService.searchGames(gameName, regionCode);
      
      if (!cjPrices || cjPrices.length === 0) {
        console.log(`⚠️ CJ REST API: No prices found for "${gameName}" from any advertisers`);
        return null;
      }

      // Look for Gamivo specifically first
      const gamivoPrice = cjPrices.find(price => 
        price.store_name.toLowerCase().includes('gamivo')
      );

      if (gamivoPrice) {
        console.log(`✅ CJ REST API: Found Gamivo price for "${gameName}": ${gamivoPrice.currency} ${gamivoPrice.price}`);
        
        return {
          store_name: 'Gamivo',
          price: this.formatPrice(gamivoPrice.price, gamivoPrice.currency),
          original_price: gamivoPrice.original_price ? this.formatPrice(gamivoPrice.original_price, gamivoPrice.currency) : undefined,
          discount_percentage: gamivoPrice.discount_percentage,
          store_url: gamivoPrice.store_url,
          affiliate_url: gamivoPrice.affiliate_url,
          currency: gamivoPrice.currency,
          availability: gamivoPrice.availability,
          region_code: regionCode
        };
      } else {
        // If no specific Gamivo, try to find any good gaming deal from other CJ partners
        const bestPrice = cjPrices[0]; // Take the first result
        console.log(`✅ CJ REST API: Found alternative gaming price from ${bestPrice.store_name}: ${bestPrice.currency} ${bestPrice.price}`);
        
        return {
          store_name: bestPrice.store_name,
          price: this.formatPrice(bestPrice.price, bestPrice.currency),
          original_price: bestPrice.original_price ? this.formatPrice(bestPrice.original_price, bestPrice.currency) : undefined,
          discount_percentage: bestPrice.discount_percentage,
          store_url: bestPrice.store_url,
          affiliate_url: bestPrice.affiliate_url,
          currency: bestPrice.currency,
          availability: bestPrice.availability,
          region_code: regionCode
        };
      }

    } catch (error) {
      console.error(`💥 CJ REST API GAMIVO ERROR:`, error);
      return null;
    }
  }

  /**
   * Format price with currency symbol
   */
  private formatPrice(price: number, currency: string): string {
    const currencySymbols: Record<string, string> = {
      'USD': '$', 'EUR': '€', 'GBP': '£', 'JPY': '¥', 'KRW': '₩',
      'BRL': 'R$', 'CAD': 'C$', 'AUD': 'A$', 'CNY': '¥', 'INR': '₹', 'RUB': '₽'
    };
    
    const symbol = currencySymbols[currency] || currency + ' ';
    return `${symbol}${price.toFixed(2)}`;
  }

  /**
   * Scrape Gamivo prices via CJ API (DEPRECATED - CJ API doesn't have product search)
   */
  async scrapeGamivo(gameName: string, regionCode: string = 'global'): Promise<PriceData | null> {
    console.log(`🚀 GAMIVO (CJ) SCRAPER: DEPRECATED - CJ API doesn't support product search`);
    return null;
  }

  /**
   * Scrape all CJ gaming stores (Fanatical, Humble Bundle, GamesPlanet, etc.)
   */
  async scrapeCJGamingStores(gameName: string, regionCode: string = 'global'): Promise<PriceData[]> {
    console.log(`🌍 CJ GAMING STORES SCRAPER: Searching for "${gameName}" in region ${regionCode}`);
    
    try {
      if (!cjRestApiService.isConfigured()) {
        console.error(`❌ CJ API not configured - missing credentials`);
        return [];
      }
      
      const cjPrices = await cjRestApiService.searchGames(gameName, regionCode);
      
      if (!cjPrices || cjPrices.length === 0) {
        console.error(`❌ CJ API returned no gaming prices for "${gameName}"`);
        return [];
      }

      console.log(`✅ CJ found ${cjPrices.length} gaming store prices for "${gameName}"`);
      
      // Convert to PriceData format
      const priceDataArray: PriceData[] = cjPrices.map((cjData: CJGamePrice) => ({
        store_name: cjData.store_name,
        price: this.formatPrice(cjData.price, cjData.currency),
        original_price: cjData.original_price ? 
          this.formatPrice(cjData.original_price, cjData.currency) : undefined,
        discount_percentage: cjData.discount_percentage,
        store_url: cjData.store_url,
        affiliate_url: cjData.affiliate_url,
        currency: cjData.currency,
        availability: cjData.availability,
        region_code: regionCode
      }));

      priceDataArray.forEach(priceData => {
        console.log(`💰 CJ/${priceData.store_name}: ${priceData.price}`);
      });
      
      return priceDataArray;
      
    } catch (error) {
      console.error(`💥 CJ GAMING STORES SCRAPER ERROR:`, error);
      return [];
    }
  }

  /**
   * Scrape prices for a game in a specific region
   */
  async scrapePricesForGame(gameName: string, regionCode: string = DEFAULT_REGION, steamAppId?: string): Promise<ScrapingResult> {
    try {
      await this.init();

      console.log(`🔍 Starting price scraping for "${gameName}" in region ${regionCode.toUpperCase()}`);

      // Only Steam supports true multi-region pricing via API
      // Other stores are region-specific by nature
      const scrapingPromises = [
        this.scrapeSteam(gameName, regionCode), // Uses Steam API with region support
        this.scrapeKinguin(gameName, regionCode), // Pass region so Kinguin can map to currency
        this.scrapeGamivoCJ(gameName, regionCode), // CJ API for Gamivo affiliate prices
        // Other stores are always scraped for their default regions
        ...(regionCode === 'br' ? [this.scrapeNuuvem(gameName, regionCode)] : [])
        // Disabled: Fanatical (CJ API unavailable), Epic Games, Instant Gaming
      ];

      console.log(`📊 SCRAPING STORES: Will scrape ${scrapingPromises.length} stores for "${gameName}" in region ${regionCode}:`, {
        stores: ['Steam', 'Kinguin', 'Gamivo', ...(regionCode === 'br' ? ['Nuuvem'] : [])],
        region: regionCode
      });

      const results = await Promise.allSettled(scrapingPromises);
      const successfulPrices: PriceData[] = [];

      results.forEach((result, index) => {
        const storeNames = ['Steam', 'Kinguin', 'Gamivo', ...(regionCode === 'br' ? ['Nuuvem'] : [])];
        const storeName = storeNames[index] || `Store ${index + 1}`;
        
        if (result.status === 'fulfilled' && result.value) {
          successfulPrices.push(result.value);
          console.log(`✅ ${storeName}: Found price ${result.value.price} from ${result.value.store_name}`);
        } else if (result.status === 'fulfilled' && !result.value) {
          console.log(`⚠️ ${storeName}: Returned null (no price found)`);
        } else if (result.status === 'rejected') {
          console.error(`❌ ${storeName}: Scraping failed:`, result.reason);
        }
      });

      console.log(`🎯 Scraping completed for "${gameName}" in region ${regionCode}: ${successfulPrices.length} prices found`);

      return {
        success: successfulPrices.length > 0,
        data: successfulPrices,
        error: successfulPrices.length === 0 ? `Nenhum preço encontrado para a região ${regionCode.toUpperCase()}` : undefined
      };
    } catch (error) {
      console.error(`Erro geral no scraping para região ${regionCode}:`, error);
      return {
        success: false,
        error: 'Erro interno do servidor'
      };
    } finally {
      await this.close();
    }
  }

  /**
   * Scrape Steam prices for multiple regions
   */
  async scrapeSteamPricesMultiRegion(gameName: string, regionCodes: string[]): Promise<ScrapingResult> {
    try {
      console.log(`🌍 Starting multi-region Steam price scraping for "${gameName}" in ${regionCodes.length} regions`);

      const steamPrices = await this.scrapeSteamMultiRegion(gameName, regionCodes);

      return {
        success: steamPrices.length > 0,
        data: steamPrices,
        error: steamPrices.length === 0 ? 'Nenhum preço Steam encontrado nas regiões especificadas' : undefined
      };
    } catch (error) {
      console.error('Erro no scraping Steam multi-região:', error);
      return {
        success: false,
        error: 'Erro interno do servidor'
      };
    }
  }

  /**
   * Update game prices in database for a specific region
   */
  async updateGamePrices(gameId: string, gameName: string, regionCode: string = DEFAULT_REGION, steamAppId?: string): Promise<boolean> {
    try {
      console.log(`🔄 Updating prices for game ${gameId} (${gameName}) in region ${regionCode}`);

      const scrapingResult = await this.scrapePricesForGame(gameName, regionCode, steamAppId);
      
      if (!scrapingResult.success || !scrapingResult.data) {
        console.log(`⚠️ No prices found for ${gameName} in region ${regionCode}`);
        return false;
      }

             const supabase = await createServerClient();

       // Insert or update prices for this region
       for (const priceData of scrapingResult.data) {
         const { error } = await supabase
          .from('game_prices')
          .upsert({
            game_id: gameId,
            store_name: priceData.store_name,
            region_code: priceData.region_code,
            price: priceData.price,
            original_price: priceData.original_price,
            discount_percentage: priceData.discount_percentage,
            store_url: priceData.store_url,
            affiliate_url: priceData.affiliate_url, // Add affiliate tracking
            currency: priceData.currency,
            availability: priceData.availability,
            last_updated: new Date().toISOString()
          }, {
            onConflict: 'game_id,store_name,region_code'
          });

        if (error) {
          console.error(`❌ Error updating ${priceData.store_name} price for region ${regionCode}:`, error);
        } else {
          console.log(`✅ Updated ${priceData.store_name} price: ${priceData.price} (${priceData.currency}) for region ${regionCode}`);
        }
      }

      return true;
    } catch (error) {
      console.error(`Error updating game prices for region ${regionCode}:`, error);
      return false;
    }
  }

  /**
   * NEW: Update game prices with Kinguin multi-currency support
   * This method updates prices for all stores, with Kinguin fetching ALL currencies at once
   */
  async updateGamePricesWithMultiCurrency(gameId: string, gameName: string, regionCode: string = DEFAULT_REGION, steamAppId?: string): Promise<boolean> {
    try {
      console.log(`🌍 Updating prices with multi-currency for game ${gameId} (${gameName}) in region ${regionCode}`);

      const supabase = await createServerClient();
      let hasAnyPrices = false;

      // 1. Get regular Steam prices for the specific region
      console.log(`🎮 Fetching Steam prices for region ${regionCode}...`);
      await this.init();
      const steamPriceData = await this.scrapeSteam(gameName, regionCode);
      
      if (steamPriceData) {
        const { error } = await supabase
          .from('game_prices')
          .upsert({
            game_id: gameId,
            store_name: steamPriceData.store_name,
            region_code: steamPriceData.region_code,
            price: steamPriceData.price,
            original_price: steamPriceData.original_price,
            discount_percentage: steamPriceData.discount_percentage,
            store_url: steamPriceData.store_url,
            affiliate_url: steamPriceData.affiliate_url,
            currency: steamPriceData.currency,
            availability: steamPriceData.availability,
            last_updated: new Date().toISOString()
          }, {
            onConflict: 'game_id,store_name,region_code'
          });

        if (error) {
          console.error(`❌ Error updating Steam price:`, error);
        } else {
          console.log(`✅ Updated Steam price: ${steamPriceData.price} (${steamPriceData.currency}) for region ${regionCode}`);
          hasAnyPrices = true;
        }
      }

      // 2. Get Kinguin prices for ALL currencies at once
      console.log(`🌍 Fetching Kinguin prices for ALL currencies...`);
      const kinguinAllCurrencies = await this.scrapeKinguinAllCurrencies(gameName);
      
      if (kinguinAllCurrencies && kinguinAllCurrencies.length > 0) {
        console.log(`💰 Updating ${kinguinAllCurrencies.length} Kinguin currency records in database...`);
        
        // Insert/update all Kinguin currency records
        for (const priceData of kinguinAllCurrencies) {
          const { error } = await supabase
            .from('game_prices')
            .upsert({
              game_id: gameId,
              store_name: priceData.store_name,
              region_code: priceData.region_code,
              price: priceData.price,
              original_price: priceData.original_price,
              discount_percentage: priceData.discount_percentage,
              store_url: priceData.store_url,
              affiliate_url: priceData.affiliate_url,
              currency: priceData.currency,
              availability: priceData.availability,
              last_updated: new Date().toISOString()
            }, {
              onConflict: 'game_id,store_name,region_code'
            });

          if (error) {
            console.error(`❌ Error updating Kinguin ${priceData.currency} price:`, error);
          } else {
            console.log(`✅ Updated Kinguin price: ${priceData.price} (${priceData.currency}) for region ${priceData.region_code}`);
            hasAnyPrices = true;
          }
        }
      }

      // 3. Get other regional stores if appropriate
      if (regionCode === 'br') {
        console.log(`🇧🇷 Fetching Nuuvem prices for Brazil...`);
        const nuuvemPriceData = await this.scrapeNuuvem(gameName, regionCode);
        
        if (nuuvemPriceData) {
          const { error } = await supabase
            .from('game_prices')
            .upsert({
              game_id: gameId,
              store_name: nuuvemPriceData.store_name,
              region_code: nuuvemPriceData.region_code,
              price: nuuvemPriceData.price,
              original_price: nuuvemPriceData.original_price,
              discount_percentage: nuuvemPriceData.discount_percentage,
              store_url: nuuvemPriceData.store_url,
              affiliate_url: nuuvemPriceData.affiliate_url,
              currency: nuuvemPriceData.currency,
              availability: nuuvemPriceData.availability,
              last_updated: new Date().toISOString()
            }, {
              onConflict: 'game_id,store_name,region_code'
            });

          if (error) {
            console.error(`❌ Error updating Nuuvem price:`, error);
          } else {
            console.log(`✅ Updated Nuuvem price: ${nuuvemPriceData.price} (${nuuvemPriceData.currency}) for region ${regionCode}`);
            hasAnyPrices = true;
          }
        }
      }

      if (regionCode === 'us' || regionCode === 'de') {
        console.log(`🌐 Fetching Epic Games and Instant Gaming prices...`);
        
        const epicPriceData = await this.scrapeEpicGames(gameName, regionCode);
        const instantGamingPriceData = await this.scrapeInstantGaming(gameName, regionCode);
        
        const otherStorePrices = [epicPriceData, instantGamingPriceData].filter(Boolean);
        
        for (const priceData of otherStorePrices) {
          if (priceData) {
            const { error } = await supabase
              .from('game_prices')
              .upsert({
                game_id: gameId,
                store_name: priceData.store_name,
                region_code: priceData.region_code,
                price: priceData.price,
                original_price: priceData.original_price,
                discount_percentage: priceData.discount_percentage,
                store_url: priceData.store_url,
                affiliate_url: priceData.affiliate_url,
                currency: priceData.currency,
                availability: priceData.availability,
                last_updated: new Date().toISOString()
              }, {
                onConflict: 'game_id,store_name,region_code'
              });

            if (error) {
              console.error(`❌ Error updating ${priceData.store_name} price:`, error);
            } else {
              console.log(`✅ Updated ${priceData.store_name} price: ${priceData.price} (${priceData.currency}) for region ${regionCode}`);
              hasAnyPrices = true;
            }
          }
        }
      }

      await this.close();

      if (hasAnyPrices) {
        console.log(`🎯 Multi-currency price update completed successfully for "${gameName}"`);
      } else {
        console.log(`⚠️ No prices found for "${gameName}" in any store or currency`);
      }

      return hasAnyPrices;
      
    } catch (error) {
      console.error(`Error updating game prices with multi-currency:`, error);
      await this.close();
      return false;
    }
  }

  /**
   * Update Steam prices for multiple regions
   */
  async updateGamePricesMultiRegion(gameId: string, gameName: string, regionCodes: string[]): Promise<boolean> {
    try {
      console.log(`🌍 Updating Steam prices for game ${gameId} (${gameName}) in ${regionCodes.length} regions`);

      const scrapingResult = await this.scrapeSteamPricesMultiRegion(gameName, regionCodes);
      
      if (!scrapingResult.success || !scrapingResult.data) {
        console.log(`⚠️ No Steam prices found for ${gameName} in specified regions`);
        return false;
      }

             const supabase = await createServerClient();

       // Insert or update prices for all regions
       for (const priceData of scrapingResult.data) {
         const { error } = await supabase
          .from('game_prices')
          .upsert({
            game_id: gameId,
            store_name: priceData.store_name,
            region_code: priceData.region_code,
            price: priceData.price,
            original_price: priceData.original_price,
            discount_percentage: priceData.discount_percentage,
            store_url: priceData.store_url,
            affiliate_url: priceData.affiliate_url, // Add affiliate tracking
            currency: priceData.currency,
            availability: priceData.availability,
            last_updated: new Date().toISOString()
          }, {
            onConflict: 'game_id,store_name,region_code'
          });

        if (error) {
          console.error(`❌ Error updating ${priceData.store_name} price for region ${priceData.region_code}:`, error);
        } else {
          console.log(`✅ Updated ${priceData.store_name} price: ${priceData.price} (${priceData.currency}) for region ${priceData.region_code}`);
        }
      }

      return true;
    } catch (error) {
      console.error('Error updating multi-region game prices:', error);
      return false;
    }
  }

  /**
   * Get all available prices for a game from various sources
   */
  async getAllPrices(gameName: string, regionCode: string = 'global'): Promise<PriceData[]> {
    console.log(`🚀 SCRAPING ALL PRICES: Searching for "${gameName}" in region ${regionCode}`);
    
    const allPrices: PriceData[] = [];
    const scrapingPromises: Promise<PriceData | null>[] = [];

    // Add scraping for all stores, including Gamivo
    scrapingPromises.push(this.scrapeFanatical(gameName, regionCode));
    scrapingPromises.push(this.scrapeGamivo(gameName, regionCode));
    
    // Add other scrapers if they exist
    // scrapingPromises.push(this.scrapeHumbleBundle(gameName, regionCode));
    // scrapingPromises.push(this.scrapeEpicGames(gameName, regionCode));

    try {
      // Wait for all scraping to complete
      const results = await Promise.allSettled(scrapingPromises);
      
      for (const result of results) {
        if (result.status === 'fulfilled' && result.value) {
          allPrices.push(result.value);
        } else if (result.status === 'rejected') {
          console.error('❌ Scraping failed:', result.reason);
        }
      }

      // Sort by price (lowest first) - convert string prices to numbers for comparison
      allPrices.sort((a, b) => {
        const priceA = parseFloat(a.price.replace(/[^\d.,]/g, '').replace(',', '.'));
        const priceB = parseFloat(b.price.replace(/[^\d.,]/g, '').replace(',', '.'));
        return priceA - priceB;
      });

      console.log(`✅ TOTAL PRICES FOUND: ${allPrices.length} para "${gameName}"`);
      allPrices.forEach(price => {
        console.log(`💰 ${price.store_name}: ${price.currency} ${price.price}${price.discount_percentage ? ` (${price.discount_percentage}% off)` : ''}`);
      });

      return allPrices;

    } catch (error) {
      console.error('❌ Error in getAllPrices:', error);
      return [];
    }
  }
}

// Legacy functions for backward compatibility
export async function atualizarPrecosJogo(game: { id: string, name: string }, regionCode: string = DEFAULT_REGION) {
  const scraper = new PriceScrapingService();
  return await scraper.updateGamePrices(game.id, game.name, regionCode);
}

export async function atualizarPrecosJogoMultiRegion(game: { id: string, name: string }, regionCodes: string[]) {
  const scraper = new PriceScrapingService();
  return await scraper.updateGamePricesMultiRegion(game.id, game.name, regionCodes);
}

export async function atualizarPrecosEmLote(games: { id: string, name: string }[], regionCode: string = DEFAULT_REGION) {
  const scraper = new PriceScrapingService();
  const results = [];
  
  for (const game of games) {
    const result = await scraper.updateGamePrices(game.id, game.name, regionCode);
    results.push(result);
  }
  
  return results;
}

// NEW: Multi-currency update functions
export async function atualizarPrecosJogoComMultiCurrency(game: { id: string, name: string }, regionCode: string = DEFAULT_REGION) {
  const scraper = new PriceScrapingService();
  return await scraper.updateGamePricesWithMultiCurrency(game.id, game.name, regionCode);
}

export async function atualizarPrecosEmLoteComMultiCurrency(games: { id: string, name: string }[], regionCode: string = DEFAULT_REGION) {
  const scraper = new PriceScrapingService();
  const results = [];
  
  for (const game of games) {
    const result = await scraper.updateGamePricesWithMultiCurrency(game.id, game.name, regionCode);
    results.push(result);
  }
  
  return results;
} 