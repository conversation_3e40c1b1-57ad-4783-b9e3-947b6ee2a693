// Manual CJ API Test Script
// Replace YOUR_DEVELOPER_KEY with your actual CJ API key

const testCJAPIManual = async () => {
  // 🔑 CONFIGURE YOUR CREDENTIALS HERE:
  const config = {
    developerKey: 'YOUR_DEVELOPER_KEY', // ← Put your CJ API key here
    baseUrl: 'https://api.cj.com',
    timeout: 10000
  };

  if (!config.developerKey || config.developerKey === 'YOUR_DEVELOPER_KEY') {
    console.log('❌ Please edit this file and add your CJ_DEVELOPER_KEY');
    console.log('💡 Get your key from: https://developers.cj.com/');
    return;
  }

  console.log('🔧 Testing CJ API Product Search Endpoints...');
  console.log(`📋 Developer Key: ${config.developerKey.substring(0, 8)}...`);

  // Product search endpoints (corrected)
  const endpoints = [
    `${config.baseUrl}/v3/product-search`,
    `${config.baseUrl}/v2/product-search`,
    `${config.baseUrl}/v3/product-catalog`,
    `${config.baseUrl}/v2/product-catalog`,
    'https://product-search.api.cj.com/v2/product-search',
    'https://product-catalog.api.cj.com/v2/product-catalog'
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`\n🔍 Testing: ${endpoint}`);
      
      const url = `${endpoint}?keywords=${encodeURIComponent('Elden Ring')}&records-per-page=5&advertiser-ids=joined`;
      console.log(`📡 Full URL: ${url}`);
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${config.developerKey}`,
          'Accept': 'application/xml',
          'User-Agent': 'CriticalPixel/1.0',
          'Content-Type': 'application/xml'
        },
        signal: AbortSignal.timeout(config.timeout)
      });

      console.log(`📊 Status: ${response.status} ${response.statusText}`);
      
      const responseText = await response.text();
      console.log(`📄 Response Length: ${responseText.length} chars`);
      console.log(`📄 Response Preview: ${responseText.substring(0, 300)}...`);

      if (response.status === 200) {
        if (responseText.includes('<?xml')) {
          console.log('✅ SUCCESS: Received XML data!');
          
          // Count products
          const productMatches = responseText.match(/<product[^>]*>[\s\S]*?<\/product>/gi) ||
                                responseText.match(/<item[^>]*>[\s\S]*?<\/item>/gi);
          
          if (productMatches) {
            console.log(`🎯 Found ${productMatches.length} product elements`);
            console.log(`🔍 First product snippet: ${productMatches[0].substring(0, 200)}...`);
            
            // Try to extract a product name
            const nameMatch = productMatches[0].match(/<name[^>]*>([^<]+)<\/name>/i);
            if (nameMatch) {
              console.log(`🎮 Product name: ${nameMatch[1]}`);
            }
            
            console.log('🎯 THIS ENDPOINT WORKS! Update your .env.local with your credentials.');
          } else {
            console.log('⚠️ XML received but no product elements found');
          }
          
          break; // Found working endpoint
        } else if (responseText.includes('<!doctype html')) {
          console.log('⚠️ Received HTML instead of XML (probably login page)');
          console.log('🔑 Check your CJ_DEVELOPER_KEY - it might be incorrect');
        } else {
          console.log('⚠️ Received unexpected response format');
        }
      } else if (response.status === 401) {
        console.log('❌ UNAUTHORIZED - Your CJ_DEVELOPER_KEY is invalid');
        console.log('💡 Get a new key from: https://developers.cj.com/');
      } else if (response.status === 400) {
        console.log('⚠️ BAD REQUEST - Endpoint exists but parameters are wrong');
        console.log(`📄 Error: ${responseText.substring(0, 200)}`);
      } else if (response.status === 403) {
        console.log('❌ FORBIDDEN - Check if your account has API access');
      } else {
        console.log(`❌ HTTP ${response.status}: ${responseText.substring(0, 200)}`);
      }

    } catch (error) {
      console.log(`❌ Network Error: ${error.message}`);
    }
  }

  console.log('\n🏁 Test Complete');
  console.log('\n📋 Next Steps:');
  console.log('1. If you found a working endpoint (✅ SUCCESS), copy your credentials to .env.local');
  console.log('2. Set CJ_FORCE_REAL_DATA=true in .env.local');
  console.log('3. Restart your Next.js server: npm run dev');
  console.log('4. Test on any game page with "Force Update"');
};

// Run the test
testCJAPIManual().catch(console.error); 